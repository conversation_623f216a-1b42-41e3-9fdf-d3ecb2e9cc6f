import React, { useState, useRef } from 'react';
import { Camera, Upload, Loader2, FileText, AlertCircle } from 'lucide-react';
import { ExpenseItem } from '../types';
import { receiptApi } from '../services/api';

interface ReceiptScannerProps {
  onScanResult: (data: {
    description: string;
    total_amount: number;
    items: ExpenseItem[];
  }) => void;
  onError: (error: string) => void;
}

const ReceiptScanner: React.FC<ReceiptScannerProps> = ({
  onScanResult,
  onError
}) => {
  const [isScanning, setIsScanning] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (file: File) => {
    if (!file.type.startsWith('image/')) {
      onError('Please select an image file');
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewImage(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    setIsScanning(true);
    onError('');

    try {
      // Call the actual receipt scanning API
      const response = await receiptApi.scanReceiptUpload(file, 'English');

      if (!response.success) {
        throw new Error(response.error || 'Failed to scan receipt');
      }

      if (!response.data) {
        throw new Error('No data returned from receipt scan');
      }

      // Convert the API response to the expected format
      const receiptData = response.data;

      // Get the total amount (use the first total if multiple currencies)
      const totalAmount = receiptData.totals.length > 0
        ? parseFloat(receiptData.totals[0].amount)
        : 0;

      // Convert itemized list to ExpenseItem format
      const items: ExpenseItem[] = receiptData.itemized_list.map((item, index) => ({
        id: (index + 1).toString(),
        name: item.translation || item.item_name,
        price: parseFloat(item.price),
        quantity: item.quantity,
        assigned_to: []
      }));

      // Create the result object
      const result = {
        description: receiptData.merchant_name || 'Receipt',
        total_amount: totalAmount,
        items: items
      };

      onScanResult(result);
    } catch (err) {
      console.error('Error processing receipt:', err);
      onError('Failed to process receipt. Please try again or enter manually.');
    } finally {
      setIsScanning(false);
    }
  };



  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const openCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { facingMode: 'environment' } // Use back camera on mobile
      });
      
      // Create video element for camera preview
      const video = document.createElement('video');
      video.srcObject = stream;
      video.play();
      
      // For now, just show file picker as camera implementation is complex
      // In a real app, you'd implement camera capture here
      fileInputRef.current?.click();
      
      // Stop the stream
      stream.getTracks().forEach(track => track.stop());
    } catch (err) {
      console.error('Camera access denied:', err);
      // Fallback to file picker
      fileInputRef.current?.click();
    }
  };

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary-400 transition-colors"
      >
        {isScanning ? (
          <div className="space-y-4">
            <Loader2 className="h-12 w-12 text-primary-600 mx-auto animate-spin" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">Processing Receipt...</h3>
              <p className="text-sm text-gray-500">
                Our AI is extracting items and amounts from your receipt
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex justify-center space-x-4">
              <button
                onClick={openCamera}
                className="flex flex-col items-center p-4 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors"
              >
                <Camera className="h-8 w-8 text-primary-600 mb-2" />
                <span className="text-sm font-medium text-primary-700">Take Photo</span>
              </button>
              
              <button
                onClick={() => fileInputRef.current?.click()}
                className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <Upload className="h-8 w-8 text-gray-600 mb-2" />
                <span className="text-sm font-medium text-gray-700">Upload Image</span>
              </button>
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-900">Scan Your Receipt</h3>
              <p className="text-sm text-gray-500">
                Take a photo or upload an image of your receipt. Our AI will automatically extract items and amounts.
              </p>
            </div>
            
            <p className="text-xs text-gray-400">
              Drag and drop an image here, or click the buttons above
            </p>
          </div>
        )}
      </div>

      {/* Preview Image */}
      {previewImage && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Receipt Preview:</h4>
          <div className="relative">
            <img
              src={previewImage}
              alt="Receipt preview"
              className="max-w-full h-auto max-h-64 mx-auto rounded-lg border border-gray-200"
            />
            {isScanning && (
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
                <div className="text-white text-center">
                  <Loader2 className="h-8 w-8 mx-auto animate-spin mb-2" />
                  <p className="text-sm">Processing...</p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <FileText className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
          <div>
            <h4 className="text-sm font-medium text-blue-900 mb-1">Tips for better scanning:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Ensure the receipt is well-lit and clearly visible</li>
              <li>• Keep the receipt flat and avoid shadows</li>
              <li>• Make sure all text is readable in the image</li>
              <li>• Include the total amount and itemized list</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Supported Formats */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          Supported formats: JPG, PNG, HEIC • Max file size: 10MB
        </p>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) {
            handleFileSelect(file);
          }
        }}
        className="hidden"
      />
    </div>
  );
};

export default ReceiptScanner;
