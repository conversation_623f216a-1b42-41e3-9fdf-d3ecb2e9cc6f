# EasySplit Environment Configuration
# Copy this file to .env and fill in your actual values

# Gemini API Key for receipt scanning
# Get your API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_api_key_here

# Database Configuration
# For development, you can use SQLite (simpler setup)
DATABASE_URL=sqlite+aiosqlite:///./easysplit.db

# For production, use PostgreSQL
# DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/easysplit

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=15
JWT_REFRESH_TOKEN_EXPIRE_DAYS=30

# Optional: Configure logging level
LOG_LEVEL=INFO

# Optional: Configure server settings
HOST=0.0.0.0
PORT=8000
