#!/usr/bin/env python3
"""
EasySplit Server Launcher
Starts the FastAPI server with proper configuration
"""

import uvicorn
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def main():
    """Start the EasySplit FastAPI server"""
    
    # Check if API key is configured
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key or api_key == "your_api_key_here":
        print("⚠️  WARNING: GEMINI_API_KEY not found in .env file")
        print("   Please create a .env file with your Gemini API key:")
        print("   GEMINI_API_KEY=your_actual_api_key_here")
        print()
    
    print("🚀 Starting EasySplit Receipt Scanner API...")
    print("📍 Server will be available at: http://localhost:8000")
    print("📖 API Documentation: http://localhost:8000/docs")
    print("🔄 Auto-reload enabled for development")
    print()
    
    # Start the server
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

if __name__ == "__main__":
    main()
