"""
Expenses router for expense management
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel
import uuid
from datetime import datetime

from database.connection import get_db
from database.models import Expense, ExpenseSplit, ExpenseItem, Group, GroupMember, User
from auth.dependencies import get_current_active_user
# from shared.schemas import MessageResponse

class MessageResponse(BaseModel):
    message: str
    success: bool

router = APIRouter(prefix="/expenses", tags=["expenses"])

# Pydantic models for request/response
class ExpenseItemCreate(BaseModel):
    name: str
    price: float
    quantity: int = 1
    assigned_to: List[str] = []

class ExpenseSplitCreate(BaseModel):
    user_id: str
    amount: float
    items: List[str] = []

class ExpenseCreate(BaseModel):
    group_id: str
    description: str
    total_amount: float
    currency: str
    category: str
    date: str
    payer_id: str
    items: List[ExpenseItemCreate] = []
    splits: List[ExpenseSplitCreate]

class ExpenseItemResponse(BaseModel):
    id: str
    name: str
    price: float
    quantity: int
    assigned_to: List[str]

class ExpenseSplitResponse(BaseModel):
    id: str
    user_id: str
    amount: float
    user_name: str
    items: List[str]

class ExpenseResponse(BaseModel):
    id: str
    group_id: str
    description: str
    total_amount: float
    currency: str
    category: str
    date: str
    payer_id: str
    payer_name: str
    items: List[ExpenseItemResponse]
    splits: List[ExpenseSplitResponse]
    created_at: str
    updated_at: str

@router.post("/", response_model=ExpenseResponse)
async def create_expense(
    expense_data: ExpenseCreate,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new expense
    """
    try:
        group_uuid = expense_data.group_id
        payer_uuid = expense_data.payer_id
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid UUID format"
        )
    
    # Verify user is member of the group
    result = await db.execute(
        select(GroupMember)
        .where(
            GroupMember.group_id == group_uuid,
            GroupMember.user_id == current_user.id
        )
    )
    
    if not result.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a member of this group"
        )
    
    # Create expense
    expense = Expense(
        group_id=group_uuid,
        title=expense_data.description,
        description=expense_data.description,
        total_amount=expense_data.total_amount,
        currency=expense_data.currency,
        category=expense_data.category,
        expense_date=datetime.fromisoformat(expense_data.date).date(),
        created_by=current_user.id
    )
    
    db.add(expense)
    await db.flush()  # Get the expense ID
    
    # Create expense items
    import json
    for item_data in expense_data.items:
        item = ExpenseItem(
            expense_id=expense.id,
            name=item_data.name,
            price=item_data.price,
            quantity=item_data.quantity,
            assigned_to=json.dumps(item_data.assigned_to) if item_data.assigned_to else None
        )
        db.add(item)

    # Create expense splits
    for split_data in expense_data.splits:
        split = ExpenseSplit(
            expense_id=expense.id,
            user_id=split_data.user_id,
            amount=split_data.amount
        )
        db.add(split)
    
    await db.commit()
    
    # Return the created expense with related data
    return await get_expense_response(expense.id, db)

@router.get("/group/{group_id}", response_model=List[ExpenseResponse])
async def get_group_expenses(
    group_id: str,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all expenses for a group
    """
    try:
        group_uuid = group_id
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid group ID format"
        )
    
    # Verify user is member of the group
    result = await db.execute(
        select(GroupMember)
        .where(
            GroupMember.group_id == group_uuid,
            GroupMember.user_id == current_user.id
        )
    )
    
    if not result.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a member of this group"
        )
    
    # Get expenses
    result = await db.execute(
        select(Expense)
        .where(Expense.group_id == group_uuid)
        .order_by(Expense.created_at.desc())
    )
    
    expenses = result.scalars().all()
    expense_responses = []
    
    for expense in expenses:
        expense_response = await get_expense_response(expense.id, db)
        expense_responses.append(expense_response)
    
    return expense_responses

@router.get("/{expense_id}", response_model=ExpenseResponse)
async def get_expense(
    expense_id: str,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific expense
    """
    try:
        expense_uuid = expense_id
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid expense ID format"
        )
    
    return await get_expense_response(expense_uuid, db)

async def get_expense_response(expense_id: uuid.UUID, db: AsyncSession) -> ExpenseResponse:
    """
    Helper function to get expense with all related data
    """
    # Get expense
    result = await db.execute(
        select(Expense)
        .where(Expense.id == expense_id)
    )
    
    expense = result.scalar_one_or_none()
    if not expense:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Expense not found"
        )
    
    # Get creator name (since we don't have payer_id in current schema)
    result = await db.execute(
        select(User.full_name, User.email)
        .where(User.id == expense.created_by)
    )
    creator = result.first()
    payer_name = creator.full_name if creator and creator.full_name else (creator.email if creator else "Unknown")
    
    # Get expense items
    result = await db.execute(
        select(ExpenseItem)
        .where(ExpenseItem.expense_id == expense_id)
    )
    items = result.scalars().all()
    
    # Get expense splits with user names
    result = await db.execute(
        select(ExpenseSplit, User.full_name, User.email)
        .join(User, ExpenseSplit.user_id == User.id)
        .where(ExpenseSplit.expense_id == expense_id)
    )
    splits_data = result.all()
    
    # Build response
    import json
    item_responses = [
        ExpenseItemResponse(
            id=str(item.id),
            name=item.name,
            price=float(item.price),
            quantity=item.quantity,
            assigned_to=json.loads(item.assigned_to) if item.assigned_to else []
        )
        for item in items
    ]

    split_responses = [
        ExpenseSplitResponse(
            id=str(split.id),
            user_id=str(split.user_id),
            amount=float(split.amount),
            user_name=user.full_name if user.full_name else user.email,
            items=[]  # Not using items in splits for now
        )
        for split, user in splits_data
    ]
    
    return ExpenseResponse(
        id=str(expense.id),
        group_id=str(expense.group_id),
        description=expense.description or expense.title,
        total_amount=float(expense.total_amount),
        currency=expense.currency,
        category=expense.category or "general",
        date=expense.expense_date.isoformat(),
        payer_id=str(expense.created_by),  # Using created_by as payer for now
        payer_name=payer_name,
        items=item_responses,
        splits=split_responses,
        created_at=expense.created_at.isoformat(),
        updated_at=expense.updated_at.isoformat()
    )
