"""
Authentication router with all auth endpoints
"""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from database.connection import get_db
from .dependencies import get_current_user, get_current_active_user
from .schemas import (
    UserCreate, UserLogin, UserUpdate, UserResponse, 
    TokenResponse, TokenRefresh, PasswordChange,
    MessageResponse, ErrorResponse
)
from .service import AuthService
from .security import JWT_ACCESS_TOKEN_EXPIRE_MINUTES

router = APIRouter(prefix="/auth", tags=["authentication"])

@router.post("/register", response_model=TokenResponse, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db)
):
    """
    Register a new user account
    
    - **email**: Valid email address (required)
    - **password**: Strong password (required, min 8 chars)
    - **confirm_password**: Must match password
    - **username**: Unique username (optional)
    - **full_name**: User's full name (optional)
    - **phone**: Phone number (optional)
    - **preferred_currency**: Default currency (optional, defaults to USD)
    - **timezone**: User's timezone (optional, defaults to UTC)
    """
    try:
        # Register user
        user = await AuthService.register_user(user_data, db)

        # Refresh user object to ensure all attributes are loaded
        await db.refresh(user)

        # Create tokens
        access_token, refresh_token = await AuthService.create_user_tokens(user, db)

        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=UserResponse.model_validate(user)
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )

@router.post("/login", response_model=TokenResponse)
async def login(
    login_data: UserLogin,
    db: AsyncSession = Depends(get_db)
):
    """
    Login with email/username and password

    - **email**: Email address or username
    - **password**: User password
    - **remember_me**: Extend refresh token lifetime (optional)
    """
    # Authenticate user
    user = await AuthService.authenticate_user(login_data, db)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email/username or password"
        )

    # Refresh user object to ensure all attributes are loaded
    await db.refresh(user)

    # Create tokens
    access_token, refresh_token = await AuthService.create_user_tokens(
        user, db, login_data.remember_me
    )

    return TokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        user=UserResponse.model_validate(user)
    )

@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    token_data: TokenRefresh,
    db: AsyncSession = Depends(get_db)
):
    """
    Refresh access token using refresh token
    
    - **refresh_token**: Valid refresh token
    """
    result = await AuthService.refresh_access_token(token_data.refresh_token, db)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    access_token, refresh_token = result
    
    # Get user for response
    from .dependencies import verify_refresh_token
    user = await verify_refresh_token(token_data.refresh_token, db)

    # Refresh user object to ensure all attributes are loaded
    if user:
        await db.refresh(user)

    return TokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        user=UserResponse.model_validate(user)
    )

@router.post("/logout", response_model=MessageResponse)
async def logout(
    token_data: TokenRefresh,
    db: AsyncSession = Depends(get_db)
):
    """
    Logout user by revoking refresh token
    
    - **refresh_token**: Refresh token to revoke
    """
    success = await AuthService.revoke_refresh_token(token_data.refresh_token, db)
    
    return MessageResponse(
        message="Logged out successfully" if success else "Token not found",
        success=success
    )

@router.post("/logout-all", response_model=MessageResponse)
async def logout_all(
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Logout from all devices by revoking all refresh tokens
    """
    count = await AuthService.revoke_all_user_tokens(current_user.id, db)
    
    return MessageResponse(
        message=f"Logged out from {count} devices",
        success=True
    )

@router.get("/me", response_model=UserResponse)
async def get_current_user_profile(
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get current user profile information
    """
    # Refresh user object to ensure all attributes are loaded
    await db.refresh(current_user)
    return UserResponse.model_validate(current_user)

@router.put("/me", response_model=UserResponse)
async def update_profile(
    update_data: UserUpdate,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update current user profile
    
    - **username**: New username (optional)
    - **full_name**: New full name (optional)
    - **phone**: New phone number (optional)
    - **preferred_currency**: New default currency (optional)
    - **timezone**: New timezone (optional)
    """
    updated_user = await AuthService.update_user_profile(current_user, update_data, db)
    # Refresh user object to ensure all attributes are loaded
    await db.refresh(updated_user)
    return UserResponse.model_validate(updated_user)

@router.post("/change-password", response_model=MessageResponse)
async def change_password(
    password_data: PasswordChange,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Change user password
    
    - **current_password**: Current password
    - **new_password**: New password (min 8 chars, strong)
    - **confirm_new_password**: Must match new password
    """
    await AuthService.change_password(
        current_user, 
        password_data.current_password, 
        password_data.new_password, 
        db
    )
    
    return MessageResponse(
        message="Password changed successfully. Please login again on all devices.",
        success=True
    )

@router.get("/validate-token")
async def validate_token(
    current_user = Depends(get_current_active_user)
):
    """
    Validate current access token
    Returns user info if token is valid
    """
    return {
        "valid": True,
        "user": UserResponse.model_validate(current_user)
    }

@router.get("/check-availability")
async def check_availability(
    email: str = None,
    username: str = None,
    db: AsyncSession = Depends(get_db)
):
    """
    Check if email or username is available for registration

    Query parameters:
    - email: Email to check
    - username: Username to check
    """
    from sqlalchemy import select, or_
    from database.models import User

    result = {
        "email_available": True,
        "username_available": True
    }

    if email:
        email_result = await db.execute(select(User).where(User.email == email))
        result["email_available"] = email_result.scalar_one_or_none() is None

    if username:
        username_result = await db.execute(select(User).where(User.username == username))
        result["username_available"] = username_result.scalar_one_or_none() is None

    return result
