import React, { useState } from 'react';
import { Loader2, AlertCircle, CheckCircle, Receipt, User, LogOut, Settings, Users } from 'lucide-react';
import FileUpload from './components/FileUpload';
import ReceiptResults from './components/ReceiptResults';
import LoginForm from './components/LoginForm';
import RegisterForm from './components/RegisterForm';
import Groups from './components/Groups';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { receiptApi } from './services/api';
import { ScanReceiptResponse } from './types';

// Main authenticated app component
function MainApp() {
  const { user, logout, isLoading } = useAuth();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [translationLanguage, setTranslationLanguage] = useState('English');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<ScanReceiptResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [currentView, setCurrentView] = useState<'scanner' | 'groups' | 'profile'>('scanner');

  const languages = [
    'English', 'Spanish', 'French', 'German', 'Italian', 
    'Portuguese', 'Dutch', 'Russian', 'Chinese', 'Japanese'
  ];

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
    setResult(null);
    setError(null);
  };

  const handleClearFile = () => {
    setSelectedFile(null);
    setResult(null);
    setError(null);
  };

  const handleScanReceipt = async () => {
    if (!selectedFile) return;

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await receiptApi.scanReceiptUpload(selectedFile, translationLanguage);
      setResult(response);
      
      if (!response.success) {
        setError(response.error || 'Failed to scan receipt');
      }
    } catch (err: any) {
      console.error('Error scanning receipt:', err);
      setError(err.response?.data?.detail || err.message || 'Failed to scan receipt');
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async () => {
    try {
      const response = await receiptApi.healthCheck();
      alert(`Backend connection successful!\n${JSON.stringify(response, null, 2)}`);
    } catch (err: any) {
      alert(`Backend connection failed!\n${err.message}`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Receipt className="h-8 w-8 text-primary-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">EasySplit</h1>
                <p className="text-sm text-gray-500">Welcome back, {user?.full_name || user?.email}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Navigation */}
              <nav className="flex space-x-4">
                <button
                  onClick={() => setCurrentView('scanner')}
                  className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    currentView === 'scanner'
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Scanner
                </button>
                <button
                  onClick={() => setCurrentView('groups')}
                  className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    currentView === 'groups'
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Groups
                </button>
                <button
                  onClick={() => setCurrentView('profile')}
                  className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    currentView === 'profile'
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Profile
                </button>
              </nav>
              
              {/* User menu */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleTestConnection}
                  className="px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                >
                  Test Backend
                </button>
                <button
                  onClick={logout}
                  className="flex items-center px-3 py-2 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
                >
                  <LogOut className="h-4 w-4 mr-1" />
                  Logout
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 py-8">
        <div className="space-y-8">
          {currentView === 'scanner' ? (
            // Receipt Scanner View
            <>
              {/* Upload Section */}
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">
                  Upload Receipt Image
                </h2>
                
                <div className="space-y-6">
                  <FileUpload
                    onFileSelect={handleFileSelect}
                    selectedFile={selectedFile}
                    onClear={handleClearFile}
                    disabled={loading}
                  />

                  {/* Language Selection */}
                  <div className="flex items-center space-x-4">
                    <label htmlFor="language" className="text-sm font-medium text-gray-700">
                      Translation Language:
                    </label>
                    <select
                      id="language"
                      value={translationLanguage}
                      onChange={(e) => setTranslationLanguage(e.target.value)}
                      disabled={loading}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50"
                    >
                      {languages.map((lang) => (
                        <option key={lang} value={lang}>
                          {lang}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Scan Button */}
                  <button
                    onClick={handleScanReceipt}
                    disabled={!selectedFile || loading}
                    className="w-full flex items-center justify-center px-4 py-3 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="animate-spin h-5 w-5 mr-2" />
                        Scanning Receipt...
                      </>
                    ) : (
                      'Scan Receipt'
                    )}
                  </button>
                </div>
              </div>

              {/* Error Display */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <AlertCircle className="h-5 w-5 text-red-400 mr-2" />
                    <h3 className="text-sm font-medium text-red-800">Error</h3>
                  </div>
                  <p className="mt-2 text-sm text-red-700">{error}</p>
                </div>
              )}

              {/* Success Display */}
              {result && result.success && result.data && (
                <div className="space-y-4">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-400 mr-2" />
                      <h3 className="text-sm font-medium text-green-800">
                        Receipt scanned successfully!
                      </h3>
                    </div>
                  </div>
                  
                  <ReceiptResults 
                    data={result.data} 
                    processingTime={result.processing_time_ms}
                  />
                </div>
              )}
            </>
          ) : currentView === 'groups' ? (
            // Groups View
            <Groups />
          ) : (
            // Profile View
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-6">
                User Profile
              </h2>
              
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Email</label>
                    <p className="mt-1 text-sm text-gray-900">{user?.email}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Username</label>
                    <p className="mt-1 text-sm text-gray-900">{user?.username || 'Not set'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Full Name</label>
                    <p className="mt-1 text-sm text-gray-900">{user?.full_name || 'Not set'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Preferred Currency</label>
                    <p className="mt-1 text-sm text-gray-900">{user?.preferred_currency}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Email Verified</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {user?.email_verified ? (
                        <span className="text-green-600">✓ Verified</span>
                      ) : (
                        <span className="text-yellow-600">⚠ Not verified</span>
                      )}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Account Created</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'Unknown'}
                    </p>
                  </div>
                </div>
                
                <div className="pt-4 border-t">
                  <button className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors">
                    Edit Profile
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}

// Authentication wrapper component
function AuthWrapper() {
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <Receipt className="mx-auto h-12 w-12 text-primary-600" />
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            EasySplit
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Modern expense splitting with AI-powered receipt scanning
          </p>
        </div>

        {authMode === 'login' ? (
          <LoginForm
            onSuccess={() => {
              // Login success is handled by the auth context
            }}
            onSwitchToRegister={() => setAuthMode('register')}
          />
        ) : (
          <RegisterForm
            onSuccess={() => {
              // Registration success is handled by the auth context
            }}
            onSwitchToLogin={() => setAuthMode('login')}
          />
        )}
      </div>
    </div>
  );
}

// Main App component with authentication
function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

// App content that responds to authentication state
function AppContent() {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="animate-spin h-8 w-8 text-primary-600 mx-auto" />
          <p className="mt-2 text-sm text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return isAuthenticated ? <MainApp /> : <AuthWrapper />;
}

export default App;
