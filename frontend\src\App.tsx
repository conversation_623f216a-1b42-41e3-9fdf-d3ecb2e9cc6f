import React, { useState, useEffect } from 'react';
import { <PERSON>rows<PERSON><PERSON>outer as Router, Routes, Route, Navigate, useParams, useNavigate, useLocation } from 'react-router-dom';
import { Loader2, AlertCircle, CheckCircle, Receipt, User, LogOut, Settings, Users } from 'lucide-react';
import DarkModeToggle from './components/DarkModeToggle';
import FileUpload from './components/FileUpload';
import ReceiptResults from './components/ReceiptResults';
import LoginForm from './components/LoginForm';
import RegisterForm from './components/RegisterForm';
import Groups from './components/Groups';
import JoinGroup from './components/JoinGroup';
import InvitationNotifications from './components/InvitationNotifications';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { receiptApi } from './services/api';
import { ScanReceiptResponse } from './types';

// Scanner component
function Scanner() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [translationLanguage, setTranslationLanguage] = useState('English');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<ScanReceiptResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  const languages = [
    'English', 'Spanish', 'French', 'German', 'Italian',
    'Portuguese', 'Dutch', 'Russian', 'Chinese', 'Japanese'
  ];

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
    setResult(null);
    setError(null);
  };

  const handleClearFile = () => {
    setSelectedFile(null);
    setResult(null);
    setError(null);
  };

  const handleScanReceipt = async () => {
    if (!selectedFile) return;

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await receiptApi.scanReceiptUpload(selectedFile, translationLanguage);
      setResult(response);

      if (!response.success) {
        setError(response.error || 'Failed to scan receipt');
      }
    } catch (err: any) {
      console.error('Error scanning receipt:', err);
      setError(err.response?.data?.detail || err.message || 'Failed to scan receipt');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Upload Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
          Upload Receipt Image
        </h2>

        <div className="space-y-6">
          <FileUpload
            onFileSelect={handleFileSelect}
            selectedFile={selectedFile}
            onClear={handleClearFile}
            disabled={loading}
          />

          {/* Language Selection */}
          <div className="flex items-center space-x-4">
            <label htmlFor="language" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Translation Language:
            </label>
            <select
              id="language"
              value={translationLanguage}
              onChange={(e) => setTranslationLanguage(e.target.value)}
              disabled={loading}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50"
            >
              {languages.map((lang) => (
                <option key={lang} value={lang}>
                  {lang}
                </option>
              ))}
            </select>
          </div>

          {/* Scan Button */}
          <button
            onClick={handleScanReceipt}
            disabled={!selectedFile || loading}
            className="w-full flex items-center justify-center px-4 py-3 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {loading ? (
              <>
                <Loader2 className="animate-spin h-5 w-5 mr-2" />
                Scanning Receipt...
              </>
            ) : (
              'Scan Receipt'
            )}
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-400 dark:text-red-300 mr-2" />
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200">Error</h3>
          </div>
          <p className="mt-2 text-sm text-red-700 dark:text-red-300">{error}</p>
        </div>
      )}

      {/* Success Display */}
      {result && result.success && result.data && (
        <div className="space-y-4">
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-400 dark:text-green-300 mr-2" />
              <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                Receipt scanned successfully!
              </h3>
            </div>
          </div>

          <ReceiptResults
            data={result.data}
            processingTime={result.processing_time_ms}
          />
        </div>
      )}
    </div>
  );
}

// Profile component
function Profile() {
  const { user } = useAuth();

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
        User Profile
      </h2>

      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
            <p className="mt-1 text-sm text-gray-900 dark:text-white">{user?.email}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Username</label>
            <p className="mt-1 text-sm text-gray-900 dark:text-white">{user?.username || 'Not set'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Full Name</label>
            <p className="mt-1 text-sm text-gray-900 dark:text-white">{user?.full_name || 'Not set'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Preferred Currency</label>
            <p className="mt-1 text-sm text-gray-900 dark:text-white">{user?.preferred_currency}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Email Verified</label>
            <p className="mt-1 text-sm text-gray-900 dark:text-white">
              {user?.email_verified ? (
                <span className="text-green-600 dark:text-green-400">✓ Verified</span>
              ) : (
                <span className="text-yellow-600 dark:text-yellow-400">⚠ Not verified</span>
              )}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Account Created</label>
            <p className="mt-1 text-sm text-gray-900 dark:text-white">
              {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'Unknown'}
            </p>
          </div>
        </div>

        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <button className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md transition-colors">
            Edit Profile
          </button>
        </div>
      </div>
    </div>
  );
}


// Main authenticated app component
function MainApp() {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [currentView, setCurrentView] = useState<'scanner' | 'groups' | 'profile'>('scanner');

  // Handle navigation state (e.g., from invite join)
  useEffect(() => {
    if (location.state?.view) {
      setCurrentView(location.state.view);
      // Clear the state to prevent it from persisting
      navigate(location.pathname, { replace: true });
    }
  }, [location.state, navigate, location.pathname]);

  const handleTestConnection = async () => {
    try {
      const response = await receiptApi.healthCheck();
      alert(`Backend connection successful!\n${JSON.stringify(response, null, 2)}`);
    } catch (err: any) {
      alert(`Backend connection failed!\n${err.message}`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors pb-20">
      {/* Mobile-First Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Receipt className="h-8 w-8 text-primary-600 dark:text-primary-400" />
              <div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">EasySplit</h1>
                <p className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-48">
                  {user?.full_name || user?.email}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <InvitationNotifications onInvitationUpdate={() => {
                // Refresh groups when invitation is accepted
                if (currentView === 'groups') {
                  window.location.reload();
                }
              }} />
              <DarkModeToggle />
              <button
                onClick={logout}
                className="p-2 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-full transition-colors"
              >
                <LogOut className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="px-4 py-4">
        {currentView === 'scanner' ? (
          <Scanner />
        ) : currentView === 'groups' ? (
          <Groups />
        ) : (
          <Profile />
        )}
      </main>

      {/* Mobile Bottom Navigation */}
      <nav className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 z-20">
        <div className="grid grid-cols-3 h-16">
          <button
            onClick={() => setCurrentView('scanner')}
            className={`flex flex-col items-center justify-center space-y-1 transition-colors ${
              currentView === 'scanner'
                ? 'text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <Receipt className="h-6 w-6" />
            <span className="text-xs font-medium">Scanner</span>
          </button>

          <button
            onClick={() => setCurrentView('groups')}
            className={`flex flex-col items-center justify-center space-y-1 transition-colors ${
              currentView === 'groups'
                ? 'text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <Users className="h-6 w-6" />
            <span className="text-xs font-medium">Groups</span>
          </button>

          <button
            onClick={() => setCurrentView('profile')}
            className={`flex flex-col items-center justify-center space-y-1 transition-colors ${
              currentView === 'profile'
                ? 'text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <User className="h-6 w-6" />
            <span className="text-xs font-medium">Profile</span>
          </button>
        </div>
      </nav>
    </div>
  );
}

// Authentication wrapper component
function AuthWrapper() {
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 transition-colors">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <Receipt className="mx-auto h-12 w-12 text-primary-600 dark:text-primary-400" />
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
            EasySplit
          </h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Modern expense splitting with AI-powered receipt scanning
          </p>
        </div>

        {authMode === 'login' ? (
          <LoginForm
            onSuccess={() => {
              // Login success is handled by the auth context
            }}
            onSwitchToRegister={() => setAuthMode('register')}
          />
        ) : (
          <RegisterForm
            onSuccess={() => {
              // Registration success is handled by the auth context
            }}
            onSwitchToLogin={() => setAuthMode('login')}
          />
        )}
      </div>
    </div>
  );
}

// Main App component with authentication
function App() {
  return (
    <Router>
      <ThemeProvider>
        <AuthProvider>
          <AppContent />
        </AuthProvider>
      </ThemeProvider>
    </Router>
  );
}

// App content that responds to authentication state
function AppContent() {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center transition-colors">
        <div className="text-center">
          <Loader2 className="animate-spin h-8 w-8 text-primary-600 dark:text-primary-400 mx-auto" />
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <Routes>
      {/* Public routes */}
      <Route path="/join/:inviteCode" element={<JoinGroup />} />

      {/* Protected routes */}
      <Route path="/*" element={
        isAuthenticated ? <MainApp /> : <AuthWrapper />
      } />
    </Routes>
  );
}

export default App;
