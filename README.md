# EasySplit - Modern Expense Splitting App

A modern expense-splitting application with AI-powered receipt scanning, multi-currency support, and real-time synchronization.

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Node.js 18+
- Gemini API Key (get from [Google AI Studio](https://makersuite.google.com/app/apikey))

### 1. Backend Setup

```bash
# Install Python dependencies
pip install -r requirements.txt

# Create environment file
cp .env.example .env

# Edit .env and add your Gemini API key
# GEMINI_API_KEY=your_actual_api_key_here
```

### 2. Start Backend Server

```bash
# Option 1: Using the launcher script (recommended)
python run_server.py

# Option 2: Direct uvicorn command
uvicorn app:app --host 0.0.0.0 --port 8000 --reload

# Option 3: Direct Python execution
python app.py
```

The backend will be available at:
- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### 3. Frontend Setup

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

The frontend will be available at: http://localhost:3000

## 📸 Testing Receipt Scanning

1. **Start both backend and frontend servers**
2. **Open http://localhost:3000 in your browser**
3. **Click "Test Backend" to verify connection**
4. **Upload a receipt image (JPG, PNG, GIF)**
5. **Select translation language**
6. **Click "Scan Receipt" to process**

## 🏗️ Project Structure

```
EasySplit/
├── app.py                      # FastAPI backend application
├── run_server.py              # Server launcher script
├── requirements.txt           # Python dependencies
├── .env.example              # Environment variables template
├── EasySplit_Architecture.md # Complete architecture documentation
├── frontend/                 # React frontend application
│   ├── src/
│   │   ├── components/       # React components
│   │   ├── services/         # API services
│   │   └── types.ts         # TypeScript types
│   ├── package.json
│   └── README.md
└── README.md                 # This file
```

## 🔧 API Endpoints

### Health Check
```http
GET /
```

### Scan Receipt (File Upload)
```http
POST /scan-receipt-upload
Content-Type: multipart/form-data

file: <image_file>
translation_language: "English"
```

### Scan Receipt (Base64)
```http
POST /scan-receipt
Content-Type: application/json

{
  "image_base64": "base64_encoded_image",
  "mime_type": "image/jpeg",
  "translation_language": "English"
}
```

## 🌍 Supported Languages

- English
- Spanish
- French
- German
- Italian
- Portuguese
- Dutch
- Russian
- Chinese
- Japanese

## 📊 Features Implemented

### ✅ Current Features
- **AI Receipt Scanning**: Extract merchant, items, totals from receipt images
- **Multi-language Support**: Translate item names to 10+ languages
- **RESTful API**: FastAPI with automatic documentation
- **Modern Frontend**: React with TypeScript and Tailwind CSS
- **Error Handling**: Comprehensive error messages and validation
- **File Upload**: Drag & drop interface with validation

### 🚧 Planned Features (from Architecture)
- **User Authentication**: JWT-based auth with OAuth
- **Group Management**: Create and manage expense groups
- **Multi-Currency**: Real-time currency conversion
- **Real-time Sync**: WebSocket-based live updates
- **Payment Integration**: PayPal, Stripe, bank connections
- **Offline Mode**: Local storage with sync
- **Mobile App**: React Native or PWA
- **Settlement Optimization**: Minimal transaction suggestions

## 🛠️ Development

### Backend Development
```bash
# Install development dependencies
pip install -r requirements.txt

# Run with auto-reload
python run_server.py

# Run tests (when implemented)
pytest
```

### Frontend Development
```bash
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Run linting
npm run lint
```

## 🔐 Environment Variables

Create a `.env` file in the root directory:

```env
# Required: Gemini API Key
GEMINI_API_KEY=your_actual_api_key_here

# Optional: Server configuration
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO
```

## 🐛 Troubleshooting

### Backend Issues
- **"GEMINI_API_KEY not found"**: Create `.env` file with your API key
- **Port 8000 in use**: Change port in `run_server.py` or kill existing process
- **Import errors**: Run `pip install -r requirements.txt`

### Frontend Issues
- **Backend connection failed**: Ensure backend is running on port 8000
- **CORS errors**: Backend includes CORS middleware for development
- **Build errors**: Run `npm install` to update dependencies

### Receipt Scanning Issues
- **No text extracted**: Try a clearer image with better lighting
- **Wrong language**: Ensure correct translation language is selected
- **API timeout**: Large images may take longer to process

## 📚 Documentation

- **Architecture**: See `EasySplit_Architecture.md` for complete system design
- **API Docs**: Visit http://localhost:8000/docs when server is running
- **Frontend**: See `frontend/README.md` for frontend-specific documentation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🔗 Links

- **Google AI Studio**: https://makersuite.google.com/app/apikey
- **FastAPI Documentation**: https://fastapi.tiangolo.com/
- **React Documentation**: https://react.dev/
- **Tailwind CSS**: https://tailwindcss.com/
