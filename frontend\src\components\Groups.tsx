import React, { useState, useEffect } from 'react';
import { Plus, Users, Search, Loader2, AlertCircle } from 'lucide-react';
import { Group, GroupCreateRequest } from '../types';
import { groupsApi } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import CreateGroupModal from './CreateGroupModal';
import GroupCard from './GroupCard';
import GroupInviteModal from './GroupInviteModal';

const Groups: React.FC = () => {
  const { user } = useAuth();
  const [groups, setGroups] = useState<Group[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const [isCreatingGroup, setIsCreatingGroup] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    loadGroups();
  }, [refreshKey]);

  // Refresh groups when component becomes visible (e.g., after joining a group)
  useEffect(() => {
    const handleFocus = () => {
      setRefreshKey(prev => prev + 1);
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, []);

  const loadGroups = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await groupsApi.getUserGroups();
      setGroups(response.groups);
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Failed to load groups');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateGroup = async (groupData: GroupCreateRequest) => {
    setIsCreatingGroup(true);
    try {
      const newGroup = await groupsApi.createGroup(groupData);
      setGroups(prev => [newGroup, ...prev]);
      setIsCreateModalOpen(false);
    } catch (error: any) {
      throw error; // Let the modal handle the error
    } finally {
      setIsCreatingGroup(false);
    }
  };

  const handleSelectGroup = (group: Group) => {
    // Navigate to group details (implement routing later)
    console.log('Selected group:', group);
  };

  const handleShareGroup = (group: Group) => {
    setSelectedGroup(group);
    setIsInviteModalOpen(true);
  };

  const filteredGroups = groups.filter(group =>
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (group.description && group.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const isUserAdmin = (group: Group) => {
    return user?.id === group.created_by;
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Groups</h1>
          <p className="text-gray-600 mt-1">
            Manage your expense groups and invite friends
          </p>
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="flex items-center px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
        >
          <Plus className="h-5 w-5 mr-2" />
          Create Group
        </button>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative max-w-md">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search groups..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
      </div>

      {/* Content */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="animate-spin h-8 w-8 text-primary-600" />
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Groups</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={loadGroups}
            className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md transition-colors"
          >
            Try Again
          </button>
        </div>
      ) : filteredGroups.length === 0 ? (
        <div className="text-center py-12">
          <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchTerm ? 'No groups found' : 'No groups yet'}
          </h3>
          <p className="text-gray-600 mb-6">
            {searchTerm 
              ? 'Try adjusting your search terms'
              : 'Create your first group to start splitting expenses with friends'
            }
          </p>
          {!searchTerm && (
            <button
              onClick={() => setIsCreateModalOpen(true)}
              className="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
            >
              Create Your First Group
            </button>
          )}
        </div>
      ) : (
        <>
          {/* Groups Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredGroups.map((group) => (
              <GroupCard
                key={group.id}
                group={group}
                onSelect={handleSelectGroup}
                onShare={handleShareGroup}
                currentUserId={user?.id}
              />
            ))}
          </div>

          {/* Stats */}
          <div className="mt-8 text-center text-sm text-gray-500">
            Showing {filteredGroups.length} of {groups.length} groups
          </div>
        </>
      )}

      {/* Modals */}
      <CreateGroupModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateGroup}
        isLoading={isCreatingGroup}
      />

      {selectedGroup && (
        <GroupInviteModal
          isOpen={isInviteModalOpen}
          onClose={() => {
            setIsInviteModalOpen(false);
            setSelectedGroup(null);
          }}
          group={selectedGroup}
          isAdmin={isUserAdmin(selectedGroup)}
        />
      )}
    </div>
  );
};

export default Groups;
