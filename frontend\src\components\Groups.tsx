import React, { useState, useEffect } from 'react';
import { Plus, Users, Search, Loader2, AlertCircle } from 'lucide-react';
import { Group, GroupCreateRequest } from '../types';
import { groupsApi } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import CreateGroupModal from './CreateGroupModal';
import GroupCard from './GroupCard';
import GroupInviteModal from './GroupInviteModal';
import GroupDetails from './GroupDetails';

const Groups: React.FC = () => {
  const { user } = useAuth();
  const [groups, setGroups] = useState<Group[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const [isCreatingGroup, setIsCreatingGroup] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [viewingGroupId, setViewingGroupId] = useState<string | null>(null);

  useEffect(() => {
    loadGroups();
  }, [refreshKey]);

  // Refresh groups when component becomes visible (e.g., after joining a group)
  useEffect(() => {
    const handleFocus = () => {
      setRefreshKey(prev => prev + 1);
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, []);

  const loadGroups = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await groupsApi.getUserGroups();
      setGroups(response.groups);
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Failed to load groups');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateGroup = async (groupData: GroupCreateRequest) => {
    setIsCreatingGroup(true);
    try {
      const newGroup = await groupsApi.createGroup(groupData);
      setGroups(prev => [newGroup, ...prev]);
      setIsCreateModalOpen(false);
    } catch (error: any) {
      throw error; // Let the modal handle the error
    } finally {
      setIsCreatingGroup(false);
    }
  };

  const handleSelectGroup = (group: Group) => {
    setViewingGroupId(group.id);
  };

  const handleBackToGroups = () => {
    setViewingGroupId(null);
  };

  const handleShareGroup = (group: Group) => {
    setSelectedGroup(group);
    setIsInviteModalOpen(true);
  };

  const filteredGroups = groups.filter(group =>
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (group.description && group.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const isUserAdmin = (group: Group) => {
    return user?.id === group.created_by;
  };

  // If viewing a specific group, show group details
  if (viewingGroupId) {
    return (
      <GroupDetails
        groupId={viewingGroupId}
        onBack={handleBackToGroups}
      />
    );
  }

  return (
    <div className="space-y-4 pb-20">
      {/* Mobile-First Header */}
      <div className="space-y-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">My Groups</h1>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            Manage your expense groups
          </p>
        </div>

        {/* Mobile Create Button */}
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="w-full flex items-center justify-center px-4 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors shadow-sm"
        >
          <Plus className="h-5 w-5 mr-2" />
          Create New Group
        </button>
      </div>

      {/* Mobile Search */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400 dark:text-gray-500" />
        </div>
        <input
          type="text"
          placeholder="Search groups..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 dark:focus:placeholder-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        />
      </div>

      {/* Content */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="animate-spin h-8 w-8 text-primary-600 dark:text-primary-400" />
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-red-500 dark:text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Error Loading Groups</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={loadGroups}
            className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md transition-colors"
          >
            Try Again
          </button>
        </div>
      ) : filteredGroups.length === 0 ? (
        <div className="text-center py-12">
          <Users className="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {searchTerm ? 'No groups found' : 'No groups yet'}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {searchTerm
              ? 'Try adjusting your search terms'
              : 'Create your first group to start splitting expenses with friends'
            }
          </p>
          {!searchTerm && (
            <button
              onClick={() => setIsCreateModalOpen(true)}
              className="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
            >
              Create Your First Group
            </button>
          )}
        </div>
      ) : (
        <>
          {/* Mobile Groups List */}
          <div className="space-y-3">
            {filteredGroups.map((group) => (
              <GroupCard
                key={group.id}
                group={group}
                onSelect={handleSelectGroup}
                onShare={handleShareGroup}
                currentUserId={user?.id}
              />
            ))}
          </div>

          {/* Mobile Stats */}
          <div className="text-center text-sm text-gray-500 dark:text-gray-400 py-4">
            {filteredGroups.length} of {groups.length} groups
          </div>
        </>
      )}

      {/* Modals */}
      <CreateGroupModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateGroup}
        isLoading={isCreatingGroup}
      />

      {selectedGroup && (
        <GroupInviteModal
          isOpen={isInviteModalOpen}
          onClose={() => {
            setIsInviteModalOpen(false);
            setSelectedGroup(null);
          }}
          group={selectedGroup}
          isAdmin={isUserAdmin(selectedGroup)}
        />
      )}

      {/* Mobile Floating Action Button */}
      <div className="fixed bottom-20 right-6 z-20">
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="w-14 h-14 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center"
        >
          <Plus className="h-7 w-7" />
        </button>
      </div>
    </div>
  );
};

export default Groups;
