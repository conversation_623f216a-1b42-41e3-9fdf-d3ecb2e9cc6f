export interface ReceiptTotal {
  amount: string;
  currency: string;
}

export interface ReceiptItem {
  item_name: string;
  translation?: string;
  quantity: number;
  price: string;
  currency: string;
}

export interface ReceiptData {
  merchant_name?: string;
  merchant_address?: string;
  date?: string;
  time?: string;
  totals: ReceiptTotal[];
  translation_language?: string;
  itemized_list: ReceiptItem[];
}

export interface ScanReceiptResponse {
  success: boolean;
  data?: ReceiptData;
  error?: string;
  processing_time_ms?: number;
}

// Authentication types
export interface User {
  id: string;
  email: string;
  username?: string;
  full_name?: string;
  phone?: string;
  preferred_currency: string;
  timezone: string;
  email_verified: boolean;
  two_factor_enabled: boolean;
  is_active: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

export interface LoginRequest {
  email: string;
  password: string;
  remember_me?: boolean;
}

export interface RegisterRequest {
  email: string;
  password: string;
  confirm_password: string;
  username?: string;
  full_name?: string;
  phone?: string;
  preferred_currency?: string;
  timezone?: string;
}

export interface TokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}
