"""
EasySplit - Modern Expense Splitting App
Complete application with authentication and receipt scanning

This module provides:
- User authentication with JWT tokens
- AI-powered receipt scanning using Google's Gemini API
- Database models for users, groups, and expenses
- RESTful API with automatic documentation
"""

import os
import json
import base64
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from contextlib import asynccontextmanager

import requests
from fastapi import FastAPI, HTTPException, UploadFile, File, Form, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field, validator
from dotenv import load_dotenv

# Import authentication components
from auth.router import router as auth_router
from auth.dependencies import get_current_active_user
from database.connection import init_db, close_db
from database.models import User

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Lifespan event handler
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await init_db()
    logger.info("Database initialized")
    yield
    # Shutdown
    await close_db()
    logger.info("Database connections closed")

# Initialize FastAPI app
app = FastAPI(
    title="EasySplit - Expense Splitting App",
    description="Modern expense splitting app with AI-powered receipt scanning, authentication, and real-time sync",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include authentication router
app.include_router(auth_router)

# Pydantic models for structured data
class ReceiptTotal(BaseModel):
    amount: str = Field(..., description="Total amount as string to preserve precision")
    currency: str = Field(..., description="Currency code (e.g., USD, EUR)")

class ReceiptItem(BaseModel):
    item_name: str = Field(..., description="Name of the item")
    translation: Optional[str] = Field(None, description="Translation of item name")
    quantity: int = Field(1, description="Quantity of the item")
    price: str = Field(..., description="Price as string to preserve precision")
    currency: str = Field(..., description="Currency code")

class ReceiptData(BaseModel):
    merchant_name: Optional[str] = Field(None, description="Name of the merchant")
    merchant_address: Optional[str] = Field(None, description="Address of the merchant")
    date: Optional[str] = Field(None, description="Date in dd.mm.yyyy format")
    time: Optional[str] = Field(None, description="Time in HH:mm:ss format")
    totals: List[ReceiptTotal] = Field(default_factory=list, description="List of totals found")
    translation_language: Optional[str] = Field(None, description="Target language for translations")
    itemized_list: List[ReceiptItem] = Field(default_factory=list, description="List of itemized items")

class ScanReceiptRequest(BaseModel):
    image_base64: str = Field(..., description="Base64 encoded image data")
    mime_type: str = Field(default="image/jpeg", description="MIME type of the image")
    translation_language: str = Field(default="English", description="Target language for item translations")

class ScanReceiptResponse(BaseModel):
    success: bool
    data: Optional[ReceiptData] = None
    error: Optional[str] = None
    processing_time_ms: Optional[int] = None

class ReceiptScannerService:
    """Service class for handling receipt scanning operations"""
    
    def __init__(self):
        self.api_key = self._get_api_key()
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent"
    
    def _get_api_key(self) -> str:
        """Get Gemini API key from environment variables"""
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key or api_key == "your_api_key_here":
            raise ValueError("Please set GEMINI_API_KEY in your .env file")
        return api_key
    
    def _create_extraction_prompt(self, language: str) -> str:
        """Create a detailed prompt for receipt data extraction"""
        return f"""You are an expert receipt analysis assistant for an expense-splitting application.
Extract all useful information from the attached receipt image with high accuracy.

IMPORTANT INSTRUCTIONS:
- Do NOT convert any prices or currencies — extract them exactly as printed
- If multiple currencies are present, include all of them
- Be precise with numbers and decimal places
- Extract ALL items, even if they seem minor (EXCEPT tips/gratuity)
- Ignore tips/gratuity as they will be handled separately
- If text is unclear, make your best interpretation but indicate uncertainty

CRITICAL TRANSLATION REQUIREMENT:
- You MUST translate ALL item names to {language}
- The "translation" field is MANDATORY and must be different from "item_name" when possible
- If the item name is already in {language}, still provide a translation (can be the same)
- For brand names (like Coca-Cola), you can keep the brand but translate descriptive parts
- Examples of good translations:
  * "Pizza Margherita" → "Margherita Pizza" (English)
  * "Cerveza Corona" → "Corona Beer" (English)
  * "Café con leche" → "Coffee with milk" (English)
  * "Schnitzel Wiener Art" → "Viennese-style Schnitzel" (English)

Return a structured JSON with these exact fields:
- merchant_name: Business name (string or null)
- merchant_address: Full address if visible (string or null)
- date: Date in dd.mm.yyyy format (string or null)
- time: Time in HH:mm:ss format (string or null)
- totals: Array of all total amounts found (exclude tips)
- translation_language: "{language}"
- itemized_list: Array of all individual items with MANDATORY translations

Example format:
{{
  "merchant_name": "Restaurant Name",
  "merchant_address": "123 Main St, City, State",
  "date": "15.12.2024",
  "time": "19:30:00",
  "totals": [
    {{"amount": "45.50", "currency": "USD"}},
    {{"amount": "41.20", "currency": "EUR"}}
  ],
  "translation_language": "{language}",
  "itemized_list": [
    {{
      "item_name": "Pivo Krušovice světlé 0,4",
      "translation": "Krušovice Light Beer 0.4L",
      "quantity": 1,
      "price": "18.50",
      "currency": "USD"
    }},
    {{
      "item_name": "Espresso doppio",
      "translation": "Double Espresso",
      "quantity": 2,
      "price": "4.00",
      "currency": "USD"
    }},
    {{
      "item_name": "Pasta Carbonara",
      "translation": "Carbonara Pasta",
      "quantity": 1,
      "price": "15.00",
      "currency": "USD"
    }}
  ]
}}

REMEMBER: Every item MUST have a meaningful translation to {language}. Do not leave translations empty or identical to the original unless the item is already in {language}.

Respond ONLY with valid JSON, no additional text or formatting."""

    async def scan_receipt(self, image_base64: str, mime_type: str, language: str) -> Dict[str, Any]:
        """
        Scan receipt image and extract structured data using Gemini API
        
        Args:
            image_base64: Base64 encoded image data
            mime_type: MIME type of the image
            language: Target language for translations
            
        Returns:
            Dictionary containing extracted receipt data
        """
        start_time = datetime.now()
        
        try:
            url = f"{self.base_url}?key={self.api_key}"
            
            headers = {"Content-Type": "application/json"}
            
            payload = {
                "contents": [{
                    "parts": [
                        {"text": self._create_extraction_prompt(language)},
                        {
                            "inline_data": {
                                "mime_type": mime_type,
                                "data": image_base64
                            }
                        }
                    ]
                }]
            }
            
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            # Extract the generated content
            if "candidates" in result and len(result["candidates"]) > 0:
                content = result["candidates"][0]["content"]["parts"][0]["text"]
                
                # Clean up the response (remove markdown formatting if present)
                content = content.strip()
                if content.startswith("```json"):
                    content = content[7:]
                if content.endswith("```"):
                    content = content[:-3]
                content = content.strip()
                
                # Parse JSON response
                extracted_data = json.loads(content)
                
                processing_time = (datetime.now() - start_time).total_seconds() * 1000
                
                return {
                    "success": True,
                    "data": extracted_data,
                    "processing_time_ms": int(processing_time)
                }
            else:
                raise ValueError("No content generated by API")
                
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            return {
                "success": False,
                "error": f"API request failed: {str(e)}"
            }
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            return {
                "success": False,
                "error": f"Failed to parse response: {str(e)}"
            }
        except Exception as e:
            logger.error(f"Unexpected error during receipt scanning: {e}")
            return {
                "success": False,
                "error": f"Unexpected error: {str(e)}"
            }

# Initialize the service
scanner_service = ReceiptScannerService()

@app.get("/")
async def root():
    """API information and health check"""
    return {
        "message": "EasySplit - Modern Expense Splitting App",
        "version": "1.0.0",
        "status": "healthy",
        "features": [
            "User Authentication (JWT)",
            "AI Receipt Scanning",
            "Multi-language Support",
            "Group Management",
            "Expense Tracking",
            "Real-time Sync"
        ],
        "endpoints": {
            "auth": "/auth",
            "docs": "/docs",
            "redoc": "/redoc",
            "receipt_scan": "/scan-receipt-upload"
        }
    }

@app.post("/scan-receipt", response_model=ScanReceiptResponse)
async def scan_receipt_endpoint(request: ScanReceiptRequest):
    """
    Scan a receipt image and extract structured data
    
    Args:
        request: ScanReceiptRequest containing image data and parameters
        
    Returns:
        ScanReceiptResponse with extracted receipt data
    """
    try:
        result = await scanner_service.scan_receipt(
            image_base64=request.image_base64,
            mime_type=request.mime_type,
            language=request.translation_language
        )
        
        if result["success"]:
            # Validate the extracted data
            receipt_data = ReceiptData(**result["data"])
            return ScanReceiptResponse(
                success=True,
                data=receipt_data,
                processing_time_ms=result.get("processing_time_ms")
            )
        else:
            return ScanReceiptResponse(
                success=False,
                error=result["error"]
            )
            
    except Exception as e:
        logger.error(f"Error in scan_receipt_endpoint: {e}")
        return ScanReceiptResponse(
            success=False,
            error=f"Internal server error: {str(e)}"
        )

@app.post("/scan-receipt-upload")
async def scan_receipt_upload(
    file: UploadFile = File(...),
    translation_language: str = Form(default="English")
):
    """
    Upload and scan a receipt image file
    
    Args:
        file: Uploaded image file
        translation_language: Target language for translations
        
    Returns:
        ScanReceiptResponse with extracted receipt data
    """
    try:
        # Validate file type
        if not file.content_type.startswith("image/"):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Read and encode image
        image_data = await file.read()
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        
        # Create request
        request = ScanReceiptRequest(
            image_base64=image_base64,
            mime_type=file.content_type,
            translation_language=translation_language
        )
        
        # Process the receipt
        return await scan_receipt_endpoint(request)
        
    except Exception as e:
        logger.error(f"Error in scan_receipt_upload: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to process image: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app:app", host="0.0.0.0", port=8000, reload=True)
