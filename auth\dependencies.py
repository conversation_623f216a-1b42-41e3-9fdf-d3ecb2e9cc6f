"""
Authentication dependencies for FastAPI
"""

import uuid
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from database.connection import get_db
from database.models import User, RefreshToken
from .security import verify_token, TokenData

# Security scheme
security = HTTPBearer()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """
    Get current authenticated user from JWT token
    
    Args:
        credentials: HTTP Bearer token
        db: Database session
        
    Returns:
        Current user object
        
    Raises:
        HTTPException: If token is invalid or user not found
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    # Verify token
    payload = verify_token(credentials.credentials)
    if payload is None:
        raise credentials_exception
    
    # Check token type
    if payload.get("type") != "access":
        raise credentials_exception
    
    # Get user ID from token
    user_id_str: str = payload.get("sub")
    if user_id_str is None:
        raise credentials_exception
    
    try:
        user_id = uuid.UUID(user_id_str)
    except ValueError:
        raise credentials_exception
    
    # Get user from database
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()
    
    if user is None:
        raise credentials_exception
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Inactive user"
        )
    
    return user

async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current active user (alias for get_current_user)
    """
    return current_user

async def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
    db: AsyncSession = Depends(get_db)
) -> Optional[User]:
    """
    Get current user if authenticated, otherwise return None
    Useful for endpoints that work for both authenticated and anonymous users
    """
    if credentials is None:
        return None
    
    try:
        return await get_current_user(credentials, db)
    except HTTPException:
        return None

async def verify_refresh_token(
    token: str,
    db: AsyncSession
) -> Optional[User]:
    """
    Verify refresh token and return associated user
    
    Args:
        token: Refresh token string
        db: Database session
        
    Returns:
        User object if token is valid, None otherwise
    """
    # Verify token structure
    payload = verify_token(token)
    if payload is None or payload.get("type") != "refresh":
        return None
    
    # Get user ID from token
    user_id_str = payload.get("sub")
    if user_id_str is None:
        return None
    
    try:
        user_id = uuid.UUID(user_id_str)
    except ValueError:
        return None
    
    # Check if refresh token exists in database and is not revoked
    from .security import hash_token
    token_hash = hash_token(token)
    
    result = await db.execute(
        select(RefreshToken)
        .where(RefreshToken.token_hash == token_hash)
        .where(RefreshToken.user_id == user_id)
        .where(RefreshToken.is_revoked == False)
    )
    refresh_token_record = result.scalar_one_or_none()
    
    if refresh_token_record is None:
        return None
    
    # Check if token is expired
    from datetime import datetime
    if refresh_token_record.expires_at < datetime.utcnow():
        return None
    
    # Get user
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()
    
    if user is None or not user.is_active:
        return None
    
    return user

class RequirePermissions:
    """
    Dependency class for checking user permissions
    """
    def __init__(self, *required_permissions: str):
        self.required_permissions = required_permissions
    
    def __call__(self, current_user: User = Depends(get_current_user)) -> User:
        """
        Check if current user has required permissions
        
        Args:
            current_user: Current authenticated user
            
        Returns:
            User object if permissions are satisfied
            
        Raises:
            HTTPException: If user lacks required permissions
        """
        # For now, we'll implement basic permission checking
        # This can be extended with role-based permissions later
        
        if not current_user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User account is inactive"
            )
        
        # Add more sophisticated permission checking here
        # For example, checking user roles, group memberships, etc.
        
        return current_user

# Common permission dependencies
require_active_user = RequirePermissions()

def require_verified_email(current_user: User = Depends(get_current_user)) -> User:
    """Require user to have verified email"""
    if not current_user.email_verified:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Email verification required"
        )
    return current_user
