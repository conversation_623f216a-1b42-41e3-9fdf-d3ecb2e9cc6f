import React, { useState } from 'react';
import { X, Save, Trash2, Refresh<PERSON><PERSON>, AlertTriangle, Users, DollarSign, FileText } from 'lucide-react';
import { Group } from '../types';
import { groupsApi } from '../services/api';

interface GroupSettingsProps {
  group: Group;
  isOpen: boolean;
  onClose: () => void;
  onGroupUpdated: (updatedGroup: Group) => void;
  isAdmin: boolean;
}

const GroupSettings: React.FC<GroupSettingsProps> = ({
  group,
  isOpen,
  onClose,
  onGroupUpdated,
  isAdmin
}) => {
  const [formData, setFormData] = useState({
    name: group.name,
    description: group.description || '',
    default_currency: group.default_currency
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showRegenerateConfirm, setShowRegenerateConfirm] = useState(false);

  const currencies = [
    'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'CNY', 'SEK', 'NZD',
    'MXN', 'SGD', 'HKD', 'NOK', 'TRY', 'RUB', 'INR', 'BRL', 'ZAR', 'KRW'
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = async () => {
    if (!isAdmin) return;
    
    setIsLoading(true);
    setError(null);

    try {
      const updatedGroup = await groupsApi.updateGroup(group.id, formData);
      onGroupUpdated(updatedGroup);
      onClose();
    } catch (err: any) {
      console.error('Error updating group:', err);
      setError(err.response?.data?.detail || err.message || 'Failed to update group');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegenerateInvite = async () => {
    if (!isAdmin) return;
    
    setIsLoading(true);
    setError(null);

    try {
      await groupsApi.regenerateInvite(group.id);
      // Refresh group data to get new invite code
      const updatedGroup = await groupsApi.getGroup(group.id);
      onGroupUpdated(updatedGroup);
      setShowRegenerateConfirm(false);
    } catch (err: any) {
      console.error('Error regenerating invite:', err);
      setError(err.response?.data?.detail || err.message || 'Failed to regenerate invite code');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteGroup = async () => {
    if (!isAdmin) return;
    
    setIsLoading(true);
    setError(null);

    try {
      await groupsApi.deleteGroup(group.id);
      onClose();
      // Navigate back to groups list
      window.location.reload(); // Simple refresh for now
    } catch (err: any) {
      console.error('Error deleting group:', err);
      setError(err.response?.data?.detail || err.message || 'Failed to delete group');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLeaveGroup = async () => {
    setIsLoading(true);
    setError(null);

    try {
      await groupsApi.leaveGroup(group.id);
      onClose();
      // Navigate back to groups list
      window.location.reload(); // Simple refresh for now
    } catch (err: any) {
      console.error('Error leaving group:', err);
      setError(err.response?.data?.detail || err.message || 'Failed to leave group');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end sm:items-center justify-center z-50 p-0 sm:p-4">
      <div className="bg-white dark:bg-gray-800 rounded-t-lg sm:rounded-lg shadow-xl w-full sm:max-w-md sm:w-full max-h-[95vh] sm:max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Group Settings</h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-red-400 mr-2" />
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          )}

          {/* Basic Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Basic Information
            </h3>

            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Group Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                disabled={!isAdmin || isLoading}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:bg-gray-50 dark:disabled:bg-gray-800"
                placeholder="Enter group name"
              />
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                disabled={!isAdmin || isLoading}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:bg-gray-50 dark:disabled:bg-gray-800"
                placeholder="Enter group description"
              />
            </div>

            <div>
              <label htmlFor="default_currency" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Default Currency
              </label>
              <select
                id="default_currency"
                name="default_currency"
                value={formData.default_currency}
                onChange={handleInputChange}
                disabled={!isAdmin || isLoading}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:bg-gray-50 dark:disabled:bg-gray-800"
              >
                {currencies.map(currency => (
                  <option key={currency} value={currency}>{currency}</option>
                ))}
              </select>
            </div>

            {isAdmin && (
              <button
                onClick={handleSave}
                disabled={isLoading}
                className="w-full flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <Save className="h-4 w-4 mr-2" />
                {isLoading ? 'Saving...' : 'Save Changes'}
              </button>
            )}
          </div>

          {/* Security Settings */}
          {isAdmin && (
            <div className="space-y-4 border-t border-gray-200 dark:border-gray-700 pt-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Security</h3>
              
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-300 mb-2">Regenerate Invite Code</h4>
                <p className="text-sm text-yellow-700 dark:text-yellow-400 mb-3">
                  This will invalidate the current invite link and create a new one.
                </p>
                {!showRegenerateConfirm ? (
                  <button
                    onClick={() => setShowRegenerateConfirm(true)}
                    disabled={isLoading}
                    className="flex items-center px-3 py-2 text-sm font-medium text-yellow-700 dark:text-yellow-300 bg-yellow-100 dark:bg-yellow-900 hover:bg-yellow-200 dark:hover:bg-yellow-800 rounded-md transition-colors"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Regenerate Invite Code
                  </button>
                ) : (
                  <div className="space-y-2">
                    <p className="text-sm text-yellow-800 dark:text-yellow-300 font-medium">Are you sure?</p>
                    <div className="flex space-x-2">
                      <button
                        onClick={handleRegenerateInvite}
                        disabled={isLoading}
                        className="px-3 py-1 text-sm bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors"
                      >
                        Yes, Regenerate
                      </button>
                      <button
                        onClick={() => setShowRegenerateConfirm(false)}
                        className="px-3 py-1 text-sm bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Danger Zone */}
          <div className="space-y-4 border-t border-gray-200 dark:border-gray-700 pt-6">
            <h3 className="text-lg font-medium text-red-600 dark:text-red-400">Danger Zone</h3>

            {isAdmin ? (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <h4 className="text-sm font-medium text-red-800 dark:text-red-300 mb-2">Delete Group</h4>
                <p className="text-sm text-red-700 dark:text-red-400 mb-3">
                  This will permanently delete the group and all its data. This action cannot be undone.
                </p>
                {!showDeleteConfirm ? (
                  <button
                    onClick={() => setShowDeleteConfirm(true)}
                    disabled={isLoading}
                    className="flex items-center px-3 py-2 text-sm font-medium text-red-700 dark:text-red-300 bg-red-100 dark:bg-red-900 hover:bg-red-200 dark:hover:bg-red-800 rounded-md transition-colors"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Group
                  </button>
                ) : (
                  <div className="space-y-2">
                    <p className="text-sm text-red-800 font-medium">Are you sure? This cannot be undone!</p>
                    <div className="flex space-x-2">
                      <button
                        onClick={handleDeleteGroup}
                        disabled={isLoading}
                        className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                      >
                        Yes, Delete
                      </button>
                      <button
                        onClick={() => setShowDeleteConfirm(false)}
                        className="px-3 py-1 text-sm bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-red-800 mb-2">Leave Group</h4>
                <p className="text-sm text-red-700 mb-3">
                  You will no longer have access to this group and its expenses.
                </p>
                <button
                  onClick={handleLeaveGroup}
                  disabled={isLoading}
                  className="flex items-center px-3 py-2 text-sm font-medium text-red-700 bg-red-100 hover:bg-red-200 rounded-md transition-colors"
                >
                  <Users className="h-4 w-4 mr-2" />
                  Leave Group
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GroupSettings;
