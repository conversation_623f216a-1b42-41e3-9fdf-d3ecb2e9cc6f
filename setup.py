#!/usr/bin/env python3
"""
Setup script for EasySplit
Installs all required dependencies and sets up the environment
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def main():
    """Main setup function"""
    print("🚀 Setting up EasySplit...")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Install backend dependencies
    if not run_command("pip install -r requirements.txt", "Installing backend dependencies"):
        print("❌ Failed to install backend dependencies")
        sys.exit(1)
    
    # Check if .env file exists
    if not os.path.exists(".env"):
        print("📝 Creating .env file from template...")
        try:
            with open(".env.example", "r") as src, open(".env", "w") as dst:
                dst.write(src.read())
            print("✅ .env file created")
            print("⚠️  Please edit .env file and add your GEMINI_API_KEY")
        except Exception as e:
            print(f"❌ Failed to create .env file: {e}")
    else:
        print("✅ .env file already exists")
    
    # Setup frontend
    frontend_dir = "frontend"
    if os.path.exists(frontend_dir):
        print(f"🔄 Setting up frontend in {frontend_dir}...")
        
        # Change to frontend directory
        original_dir = os.getcwd()
        os.chdir(frontend_dir)
        
        try:
            # Install frontend dependencies
            if not run_command("npm install", "Installing frontend dependencies"):
                print("❌ Failed to install frontend dependencies")
                os.chdir(original_dir)
                sys.exit(1)
            
            print("✅ Frontend setup completed")
        finally:
            os.chdir(original_dir)
    else:
        print("⚠️  Frontend directory not found, skipping frontend setup")
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit .env file and add your GEMINI_API_KEY")
    print("2. Start the backend: python run_server.py")
    print("3. Start the frontend: cd frontend && npm run dev")
    print("4. Visit http://localhost:3000 to use the app")
    print("\n📚 Documentation:")
    print("- API docs: http://localhost:8000/docs")
    print("- README.md for detailed instructions")

if __name__ == "__main__":
    main()
