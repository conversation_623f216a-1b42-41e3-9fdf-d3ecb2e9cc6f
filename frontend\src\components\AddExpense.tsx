import React, { useState, useEffect } from 'react';
import { X, Plus, Camera, Receipt, Calculator, Users, DollarSign, Paperclip } from 'lucide-react';
import { Group, GroupMember, ExpenseItem } from '../types';
import { expensesApi } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import ReceiptScanner from './ReceiptScanner';
import ExpenseSplitter from './ExpenseSplitter';
import CurrencyButton from './CurrencyButton';
import AttachmentUploader from './AttachmentUploader';

interface AddExpenseProps {
  group: Group;
  members: GroupMember[];
  isOpen: boolean;
  onClose: () => void;
  onExpenseAdded: () => void;
}

interface ExpenseFormData {
  description: string;
  total_amount: number;
  currency: string;
  comment: string;
  attachments: string[];
  date: string;
  items: ExpenseItem[];
  payer_id: string;
}

const AddExpense: React.FC<AddExpenseProps> = ({
  group,
  members,
  isOpen,
  onClose,
  onExpenseAdded
}) => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'manual' | 'scanner'>('manual');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSplitter, setShowSplitter] = useState(false);
  
  const [formData, setFormData] = useState<ExpenseFormData>({
    description: '',
    total_amount: 0,
    currency: group.default_currency,
    comment: '',
    attachments: [],
    date: new Date().toISOString().split('T')[0],
    items: [],
    payer_id: user?.id || ''
  });



  useEffect(() => {
    if (isOpen) {
      // Reset form when modal opens
      setFormData({
        description: '',
        total_amount: 0,
        currency: group.default_currency,
        comment: '',
        attachments: [],
        date: new Date().toISOString().split('T')[0],
        items: [],
        payer_id: user?.id || ''
      });
      setActiveTab('manual');
      setShowSplitter(false);
      setError(null);
    }
  }, [isOpen, group.default_currency, user?.id]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'total_amount' ? parseFloat(value) || 0 : value
    }));
  };

  const addExpenseItem = () => {
    const newItem: ExpenseItem = {
      id: Date.now().toString(),
      name: '',
      price: 0,
      quantity: 1,
      assigned_to: []
    };
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, newItem]
    }));
  };

  const updateExpenseItem = (itemId: string, updates: Partial<ExpenseItem>) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.id === itemId ? { ...item, ...updates } : item
      )
    }));
  };

  const removeExpenseItem = (itemId: string) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId)
    }));
  };

  const calculateTotalFromItems = () => {
    const total = formData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    setFormData(prev => ({ ...prev, total_amount: total }));
  };

  const handleScannerResult = (scannedData: {
    description: string;
    total_amount: number;
    currency?: string;
    items: ExpenseItem[];
    attachments?: string[];
  }) => {
    setFormData(prev => ({
      ...prev,
      description: scannedData.description,
      total_amount: scannedData.total_amount,
      currency: scannedData.currency || prev.currency, // Use scanned currency or keep current
      items: scannedData.items,
      attachments: [...prev.attachments, ...(scannedData.attachments || [])]
    }));
    setActiveTab('manual'); // Switch to manual tab to review/edit
  };

  const handleNext = () => {
    if (!formData.description.trim()) {
      setError('Please enter a description');
      return;
    }
    if (formData.total_amount <= 0) {
      setError('Please enter a valid amount');
      return;
    }
    if (!formData.payer_id) {
      setError('Please select who paid');
      return;
    }
    
    setError(null);
    setShowSplitter(true);
  };

  const handleSubmit = async (splits: any[]) => {
    setIsLoading(true);
    setError(null);

    try {
      const expenseData = {
        ...formData,
        group_id: group.id,
        splits
      };

      await expensesApi.createExpense(expenseData);
      onExpenseAdded();
      onClose();
    } catch (err: any) {
      console.error('Error creating expense:', err);
      setError(err.response?.data?.detail || err.message || 'Failed to create expense');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  if (showSplitter) {
    return (
      <ExpenseSplitter
        expense={formData}
        members={members}
        onBack={() => setShowSplitter(false)}
        onSubmit={handleSubmit}
        isLoading={isLoading}
        error={error}
      />
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end sm:items-center justify-center z-50 p-0 sm:p-4">
      <div className="bg-white dark:bg-gray-800 rounded-t-lg sm:rounded-lg shadow-xl w-full sm:max-w-2xl sm:w-full max-h-[95vh] sm:max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Add Expense</h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('manual')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'manual'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <Calculator className="h-4 w-4 inline mr-2" />
              Manual Entry
            </button>
            <button
              onClick={() => setActiveTab('scanner')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'scanner'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <Camera className="h-4 w-4 inline mr-2" />
              Scan Receipt
            </button>
          </nav>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Error Display */}
          {error && (
            <div className="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
              <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          )}

          {activeTab === 'manual' ? (
            <div className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Description *
                  </label>
                  <input
                    type="text"
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="e.g., Dinner at restaurant"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label htmlFor="total_amount" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Total Amount *
                  </label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                    <input
                      type="number"
                      id="total_amount"
                      name="total_amount"
                      value={formData.total_amount || ''}
                      onChange={handleInputChange}
                      step="0.01"
                      min="0"
                      placeholder="0.00"
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Currency
                  </label>
                  <CurrencyButton
                    selectedCurrency={formData.currency}
                    onCurrencyChange={(currency) => setFormData(prev => ({ ...prev, currency }))}
                    className="w-full"
                  />
                </div>

                <div>
                  <label htmlFor="comment" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Comment (optional)
                  </label>
                  <textarea
                    id="comment"
                    name="comment"
                    value={formData.comment}
                    onChange={handleInputChange}
                    placeholder="Add any additional notes about this expense..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    <Paperclip className="h-4 w-4 inline mr-1" />
                    Attachments (optional)
                  </label>
                  <AttachmentUploader
                    attachments={formData.attachments}
                    onAttachmentsChange={(attachments) => setFormData(prev => ({ ...prev, attachments }))}
                    maxFiles={3}
                  />
                </div>

                <div>
                  <label htmlFor="date" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Date
                  </label>
                  <input
                    type="date"
                    id="date"
                    name="date"
                    value={formData.date}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label htmlFor="payer_id" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Paid by *
                  </label>
                  <select
                    id="payer_id"
                    name="payer_id"
                    value={formData.payer_id}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="">Select who paid</option>
                    {members.map(member => (
                      <option key={member.user_id} value={member.user_id}>
                        {member.user_full_name || member.user_email}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Continue to next step */}
              <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={handleNext}
                  disabled={isLoading}
                  className="flex items-center px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <Users className="h-4 w-4 mr-2" />
                  Next: Split Expense
                </button>
              </div>
            </div>
          ) : (
            <ReceiptScanner
              onScanResult={handleScannerResult}
              onError={setError}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default AddExpense;
