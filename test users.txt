g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com
UsernameA
Firstname
Abcdef1234!


<EMAIL>
testusername
test
Abcdef1234!

<EMAIL>
user3
user3
Abcdef1234!


creator <EMAIL>
http://localhost:3000/join/KEYX18ML


when i use a invite link like http://localhost:3000/join/KEYX18ML with another account nothing happens, also make the invite code more unqie so no one can spam random ids to join stranger groups:
INFO:     127.0.0.1:50973 - "GET /groups/ HTTP/1.1" 200 OK
2025-06-22 19:28:42,731 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:50974 - "GET /groups/ HTTP/1.1" 200 OK
2025-06-22 19:28:42,732 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-06-22 19:28:54,837 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-22 19:28:54,837 INFO sqlalchemy.engine.Engine SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
2025-06-22 19:28:54,837 INFO sqlalchemy.engine.Engine [cached since 141s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:sqlalchemy.engine.Engine:[cached since 141s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
2025-06-22 19:28:54,838 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-22 19:28:54,838 INFO sqlalchemy.engine.Engine SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
2025-06-22 19:28:54,838 INFO sqlalchemy.engine.Engine [cached since 141s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:sqlalchemy.engine.Engine:[cached since 141s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:     127.0.0.1:50986 - "GET /auth/validate-token HTTP/1.1" 200 OK
2025-06-22 19:28:54,840 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:50987 - "GET /auth/validate-token HTTP/1.1" 200 OK
2025-06-22 19:28:54,841 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-06-22 19:29:04,720 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-22 19:29:04,720 INFO sqlalchemy.engine.Engine SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
2025-06-22 19:29:04,720 INFO sqlalchemy.engine.Engine [cached since 150.9s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:sqlalchemy.engine.Engine:[cached since 150.9s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
2025-06-22 19:29:04,721 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-22 19:29:04,721 INFO sqlalchemy.engine.Engine SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
2025-06-22 19:29:04,721 INFO sqlalchemy.engine.Engine [cached since 150.9s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:sqlalchemy.engine.Engine:[cached since 150.9s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
2025-06-22 19:29:04,722 INFO sqlalchemy.engine.Engine SELECT groups.id, groups.name, groups.description, groups.avatar_url, groups.invite_code, groups.default_currency, groups.created_by, groups.is_active, groups.created_at, groups.updated_at
FROM groups JOIN group_members ON groups.id = group_members.group_id
WHERE group_members.user_id = ? AND groups.is_active = 1 ORDER BY groups.created_at DESC
INFO:sqlalchemy.engine.Engine:SELECT groups.id, groups.name, groups.description, groups.avatar_url, groups.invite_code, groups.default_currency, groups.created_by, groups.is_active, groups.created_at, groups.updated_at
FROM groups JOIN group_members ON groups.id = group_members.group_id
WHERE group_members.user_id = ? AND groups.is_active = 1 ORDER BY groups.created_at DESC
2025-06-22 19:29:04,722 INFO sqlalchemy.engine.Engine [cached since 35.76s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:sqlalchemy.engine.Engine:[cached since 35.76s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
2025-06-22 19:29:04,723 INFO sqlalchemy.engine.Engine SELECT groups.id, groups.name, groups.description, groups.avatar_url, groups.invite_code, groups.default_currency, groups.created_by, groups.is_active, groups.created_at, groups.updated_at
FROM groups JOIN group_members ON groups.id = group_members.group_id
WHERE group_members.user_id = ? AND groups.is_active = 1 ORDER BY groups.created_at DESC
INFO:sqlalchemy.engine.Engine:SELECT groups.id, groups.name, groups.description, groups.avatar_url, groups.invite_code, groups.default_currency, groups.created_by, groups.is_active, groups.created_at, groups.updated_at
FROM groups JOIN group_members ON groups.id = group_members.group_id
WHERE group_members.user_id = ? AND groups.is_active = 1 ORDER BY groups.created_at DESC
2025-06-22 19:29:04,723 INFO sqlalchemy.engine.Engine [cached since 35.76s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:sqlalchemy.engine.Engine:[cached since 35.76s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:     127.0.0.1:50989 - "GET /groups/ HTTP/1.1" 200 OK
2025-06-22 19:29:04,724 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:50990 - "GET /groups/ HTTP/1.1" 200 OK
2025-06-22 19:29:04,725 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-06-22 19:29:29,108 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-22 19:29:29,109 INFO sqlalchemy.engine.Engine SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
2025-06-22 19:29:29,109 INFO sqlalchemy.engine.Engine [cached since 175.3s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:sqlalchemy.engine.Engine:[cached since 175.3s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
2025-06-22 19:29:29,110 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-22 19:29:29,111 INFO sqlalchemy.engine.Engine SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
2025-06-22 19:29:29,111 INFO sqlalchemy.engine.Engine [cached since 175.3s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:sqlalchemy.engine.Engine:[cached since 175.3s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:     127.0.0.1:51064 - "GET /auth/validate-token HTTP/1.1" 200 OK
2025-06-22 19:29:29,112 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:51065 - "GET /auth/validate-token HTTP/1.1" 200 OK
2025-06-22 19:29:29,113 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-06-22 19:29:34,288 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-22 19:29:34,288 INFO sqlalchemy.engine.Engine SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
2025-06-22 19:29:34,288 INFO sqlalchemy.engine.Engine [cached since 180.4s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:sqlalchemy.engine.Engine:[cached since 180.4s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
2025-06-22 19:29:34,289 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-22 19:29:34,289 INFO sqlalchemy.engine.Engine SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
2025-06-22 19:29:34,289 INFO sqlalchemy.engine.Engine [cached since 180.4s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:sqlalchemy.engine.Engine:[cached since 180.4s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
2025-06-22 19:29:34,290 INFO sqlalchemy.engine.Engine SELECT groups.id, groups.name, groups.description, groups.avatar_url, groups.invite_code, groups.default_currency, groups.created_by, groups.is_active, groups.created_at, groups.updated_at
FROM groups JOIN group_members ON groups.id = group_members.group_id
WHERE group_members.user_id = ? AND groups.is_active = 1 ORDER BY groups.created_at DESC
INFO:sqlalchemy.engine.Engine:SELECT groups.id, groups.name, groups.description, groups.avatar_url, groups.invite_code, groups.default_currency, groups.created_by, groups.is_active, groups.created_at, groups.updated_at
FROM groups JOIN group_members ON groups.id = group_members.group_id
WHERE group_members.user_id = ? AND groups.is_active = 1 ORDER BY groups.created_at DESC
2025-06-22 19:29:34,290 INFO sqlalchemy.engine.Engine [cached since 65.33s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:sqlalchemy.engine.Engine:[cached since 65.33s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
2025-06-22 19:29:34,291 INFO sqlalchemy.engine.Engine SELECT groups.id, groups.name, groups.description, groups.avatar_url, groups.invite_code, groups.default_currency, groups.created_by, groups.is_active, groups.created_at, groups.updated_at
FROM groups JOIN group_members ON groups.id = group_members.group_id
WHERE group_members.user_id = ? AND groups.is_active = 1 ORDER BY groups.created_at DESC
INFO:sqlalchemy.engine.Engine:SELECT groups.id, groups.name, groups.description, groups.avatar_url, groups.invite_code, groups.default_currency, groups.created_by, groups.is_active, groups.created_at, groups.updated_at
FROM groups JOIN group_members ON groups.id = group_members.group_id
WHERE group_members.user_id = ? AND groups.is_active = 1 ORDER BY groups.created_at DESC
2025-06-22 19:29:34,291 INFO sqlalchemy.engine.Engine [cached since 65.33s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:sqlalchemy.engine.Engine:[cached since 65.33s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:     127.0.0.1:51069 - "GET /groups/ HTTP/1.1" 200 OK
2025-06-22 19:29:34,292 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:51070 - "GET /groups/ HTTP/1.1" 200 OK
2025-06-22 19:29:34,293 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-06-22 19:29:40,009 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-22 19:29:40,009 INFO sqlalchemy.engine.Engine SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
2025-06-22 19:29:40,010 INFO sqlalchemy.engine.Engine [cached since 186.2s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:sqlalchemy.engine.Engine:[cached since 186.2s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
2025-06-22 19:29:40,011 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-22 19:29:40,011 INFO sqlalchemy.engine.Engine SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
2025-06-22 19:29:40,011 INFO sqlalchemy.engine.Engine [cached since 186.2s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:sqlalchemy.engine.Engine:[cached since 186.2s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
2025-06-22 19:29:40,012 INFO sqlalchemy.engine.Engine SELECT groups.id, groups.name, groups.description, groups.avatar_url, groups.invite_code, groups.default_currency, groups.created_by, groups.is_active, groups.created_at, groups.updated_at
FROM groups JOIN group_members ON groups.id = group_members.group_id
WHERE group_members.user_id = ? AND groups.is_active = 1 ORDER BY groups.created_at DESC
INFO:sqlalchemy.engine.Engine:SELECT groups.id, groups.name, groups.description, groups.avatar_url, groups.invite_code, groups.default_currency, groups.created_by, groups.is_active, groups.created_at, groups.updated_at
FROM groups JOIN group_members ON groups.id = group_members.group_id
WHERE group_members.user_id = ? AND groups.is_active = 1 ORDER BY groups.created_at DESC
2025-06-22 19:29:40,013 INFO sqlalchemy.engine.Engine [cached since 71.05s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:sqlalchemy.engine.Engine:[cached since 71.05s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
2025-06-22 19:29:40,014 INFO sqlalchemy.engine.Engine SELECT groups.id, groups.name, groups.description, groups.avatar_url, groups.invite_code, groups.default_currency, groups.created_by, groups.is_active, groups.created_at, groups.updated_at
FROM groups JOIN group_members ON groups.id = group_members.group_id
WHERE group_members.user_id = ? AND groups.is_active = 1 ORDER BY groups.created_at DESC
INFO:sqlalchemy.engine.Engine:SELECT groups.id, groups.name, groups.description, groups.avatar_url, groups.invite_code, groups.default_currency, groups.created_by, groups.is_active, groups.created_at, groups.updated_at
FROM groups JOIN group_members ON groups.id = group_members.group_id
WHERE group_members.user_id = ? AND groups.is_active = 1 ORDER BY groups.created_at DESC
2025-06-22 19:29:40,014 INFO sqlalchemy.engine.Engine [cached since 71.06s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:sqlalchemy.engine.Engine:[cached since 71.06s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:     127.0.0.1:51071 - "GET /groups/ HTTP/1.1" 200 OK
2025-06-22 19:29:40,015 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:51072 - "GET /groups/ HTTP/1.1" 200 OK
2025-06-22 19:29:40,016 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-06-22 19:29:48,556 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-22 19:29:48,557 INFO sqlalchemy.engine.Engine SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
2025-06-22 19:29:48,557 INFO sqlalchemy.engine.Engine [cached since 194.7s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:sqlalchemy.engine.Engine:[cached since 194.7s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
2025-06-22 19:29:48,558 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-22 19:29:48,558 INFO sqlalchemy.engine.Engine SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.email, users.username, users.full_name, users.password_hash, users.avatar_url, users.phone, users.preferred_currency, users.timezone, users.two_factor_enabled, users.two_factor_secret, users.email_verified, users.email_verification_token, users.password_reset_token, users.password_reset_expires, users.is_active, users.last_login, users.created_at, users.updated_at
FROM users
WHERE users.id = ?
2025-06-22 19:29:48,558 INFO sqlalchemy.engine.Engine [cached since 194.7s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:sqlalchemy.engine.Engine:[cached since 194.7s ago] ('f781deb99caa46fc8977d7e5d75dd638',)
INFO:     127.0.0.1:51094 - "GET /auth/validate-token HTTP/1.1" 200 OK
2025-06-22 19:29:48,559 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:51095 - "GET /auth/validate-token HTTP/1.1" 200 OK
2025-06-22 19:29:48,560 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
