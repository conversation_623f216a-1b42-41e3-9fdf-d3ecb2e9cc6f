import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Users, Loader2, AlertCircle, CheckCircle, ArrowLeft } from 'lucide-react';
import { Group, JoinGroupRequest } from '../types';
import { groupsApi } from '../services/api';
import { useAuth } from '../contexts/AuthContext';

const JoinGroup: React.FC = () => {
  const { inviteCode } = useParams<{ inviteCode: string }>();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  
  const [isJoining, setIsJoining] = useState(false);
  const [joinedGroup, setJoinedGroup] = useState<Group | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [manualCode, setManualCode] = useState('');

  useEffect(() => {
    if (inviteCode && isAuthenticated) {
      handleJoinGroup(inviteCode);
    }
  }, [inviteCode, isAuthenticated]);

  const handleJoinGroup = async (code: string) => {
    if (!code.trim()) {
      setError('Please enter an invite code');
      return;
    }

    setIsJoining(true);
    setError(null);
    
    try {
      const joinData: JoinGroupRequest = { invite_code: code.trim() };
      const group = await groupsApi.joinGroup(joinData);
      setJoinedGroup(group);
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Failed to join group';
      setError(errorMessage);
    } finally {
      setIsJoining(false);
    }
  };

  const handleManualJoin = (e: React.FormEvent) => {
    e.preventDefault();
    handleJoinGroup(manualCode);
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
          <Users className="h-12 w-12 text-primary-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Join Group</h2>
          <p className="text-gray-600 mb-6">
            You need to be logged in to join a group
          </p>
          <button
            onClick={() => navigate('/auth')}
            className="w-full px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md transition-colors"
          >
            Sign In
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        {/* Header */}
        <div className="text-center mb-6">
          <Users className="h-12 w-12 text-primary-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900">Join Group</h2>
          <p className="text-gray-600 mt-1">
            Enter an invite code to join a group
          </p>
        </div>

        {/* Success State */}
        {joinedGroup ? (
          <div className="text-center">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Successfully Joined!
            </h3>
            <p className="text-gray-600 mb-2">
              You are now a member of
            </p>
            <p className="text-xl font-semibold text-primary-600 mb-6">
              {joinedGroup.name}
            </p>
            
            <div className="space-y-3">
              <button
                onClick={() => navigate('/groups')}
                className="w-full px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md transition-colors"
              >
                View My Groups
              </button>
              <button
                onClick={() => navigate('/')}
                className="w-full px-4 py-2 text-primary-600 bg-primary-50 hover:bg-primary-100 rounded-md transition-colors"
              >
                Go to Dashboard
              </button>
            </div>
          </div>
        ) : (
          <>
            {/* Join Form */}
            <form onSubmit={handleManualJoin} className="space-y-4">
              <div>
                <label htmlFor="inviteCode" className="block text-sm font-medium text-gray-700 mb-2">
                  Invite Code
                </label>
                <input
                  id="inviteCode"
                  type="text"
                  value={manualCode || inviteCode || ''}
                  onChange={(e) => setManualCode(e.target.value)}
                  placeholder="Enter invite code"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  disabled={isJoining}
                  maxLength={10}
                />
              </div>

              {/* Error Message */}
              {error && (
                <div className="flex items-center space-x-2 text-red-600 bg-red-50 border border-red-200 rounded-md p-3">
                  <AlertCircle className="h-5 w-5 flex-shrink-0" />
                  <span className="text-sm">{error}</span>
                </div>
              )}

              {/* Submit Button */}
              <button
                type="submit"
                disabled={isJoining || (!manualCode && !inviteCode)}
                className="w-full flex items-center justify-center px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isJoining ? (
                  <>
                    <Loader2 className="animate-spin h-4 w-4 mr-2" />
                    Joining...
                  </>
                ) : (
                  'Join Group'
                )}
              </button>
            </form>

            {/* Back Button */}
            <div className="mt-6 pt-4 border-t">
              <button
                onClick={() => navigate('/groups')}
                className="w-full flex items-center justify-center px-4 py-2 text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Groups
              </button>
            </div>

            {/* Help Text */}
            <div className="mt-4 text-center">
              <p className="text-xs text-gray-500">
                Don't have an invite code? Ask a group member to share one with you.
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default JoinGroup;
