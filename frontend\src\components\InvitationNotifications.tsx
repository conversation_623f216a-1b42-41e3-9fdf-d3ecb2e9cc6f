import React, { useState, useEffect } from 'react';
import { Bell, Check, X, Users, Calendar } from 'lucide-react';
import { groupsApi } from '../services/api';

interface Invitation {
  id: string;
  group: {
    id: string;
    name: string;
    description: string;
    avatar_url?: string;
  };
  inviter: {
    id: string;
    full_name: string;
    email: string;
    avatar_url?: string;
  };
  message?: string;
  created_at: string;
}

interface InvitationNotificationsProps {
  onInvitationUpdate?: () => void;
}

const InvitationNotifications: React.FC<InvitationNotificationsProps> = ({ onInvitationUpdate }) => {
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    loadInvitations();
  }, []);

  const loadInvitations = async () => {
    try {
      const data = await groupsApi.getReceivedInvitations();
      setInvitations(data);
    } catch (err) {
      console.error('Error loading invitations:', err);
      setError('Failed to load invitations');
    }
  };

  const handleAccept = async (invitationId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      await groupsApi.acceptInvitation(invitationId);
      setInvitations(prev => prev.filter(inv => inv.id !== invitationId));
      onInvitationUpdate?.();
    } catch (err: any) {
      console.error('Error accepting invitation:', err);
      setError(err.response?.data?.detail || err.message || 'Failed to accept invitation');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDecline = async (invitationId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      await groupsApi.declineInvitation(invitationId);
      setInvitations(prev => prev.filter(inv => inv.id !== invitationId));
      onInvitationUpdate?.();
    } catch (err: any) {
      console.error('Error declining invitation:', err);
      setError(err.response?.data?.detail || err.message || 'Failed to decline invitation');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (invitations.length === 0) {
    return null;
  }

  return (
    <div className="relative">
      {/* Notification Bell */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 transition-colors"
      >
        <Bell className="h-6 w-6" />
        {invitations.length > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {invitations.length}
          </span>
        )}
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Group Invitations</h3>
          </div>

          {error && (
            <div className="p-4 bg-red-50 border-b border-red-200">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}

          <div className="max-h-96 overflow-y-auto">
            {invitations.map((invitation) => (
              <div key={invitation.id} className="p-4 border-b border-gray-100 last:border-b-0">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    {invitation.group.avatar_url ? (
                      <img
                        src={invitation.group.avatar_url}
                        alt={invitation.group.name}
                        className="h-10 w-10 rounded-full"
                      />
                    ) : (
                      <div className="h-10 w-10 bg-primary-100 rounded-full flex items-center justify-center">
                        <Users className="h-5 w-5 text-primary-600" />
                      </div>
                    )}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="text-sm">
                      <p className="text-gray-900">
                        <span className="font-medium">{invitation.inviter.full_name || invitation.inviter.email}</span>
                        {' '}invited you to join{' '}
                        <span className="font-medium">{invitation.group.name}</span>
                      </p>
                    </div>

                    {invitation.group.description && (
                      <p className="text-xs text-gray-500 mt-1">{invitation.group.description}</p>
                    )}

                    <div className="flex items-center text-xs text-gray-400 mt-2">
                      <Calendar className="h-3 w-3 mr-1" />
                      {formatDate(invitation.created_at)}
                    </div>

                    <div className="flex space-x-2 mt-3">
                      <button
                        onClick={() => handleAccept(invitation.id)}
                        disabled={isLoading}
                        className="flex items-center px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 transition-colors"
                      >
                        <Check className="h-3 w-3 mr-1" />
                        Accept
                      </button>
                      <button
                        onClick={() => handleDecline(invitation.id)}
                        disabled={isLoading}
                        className="flex items-center px-3 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700 disabled:opacity-50 transition-colors"
                      >
                        <X className="h-3 w-3 mr-1" />
                        Decline
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default InvitationNotifications;
