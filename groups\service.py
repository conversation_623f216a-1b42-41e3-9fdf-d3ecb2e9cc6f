"""
Group service with business logic
"""

import uuid
import qrcode
import io
import base64
from datetime import datetime
from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from fastapi import HTTPException, status

from database.models import Group, GroupMember, User
from auth.security import generate_invite_code
from .schemas import GroupCreate, GroupUpdate, GroupMemberUpdate

class GroupService:
    """Group service class"""
    
    @staticmethod
    async def create_group(group_data: GroupCreate, creator_id: uuid.UUID, db: AsyncSession) -> Group:
        """
        Create a new group
        
        Args:
            group_data: Group creation data
            creator_id: ID of the user creating the group
            db: Database session
            
        Returns:
            Created group object
        """
        # Generate unique invite code
        invite_code = generate_invite_code()
        
        # Ensure invite code is unique
        while True:
            result = await db.execute(select(Group).where(Group.invite_code == invite_code))
            if not result.scalar_one_or_none():
                break
            invite_code = generate_invite_code()
        
        # Create group
        db_group = Group(
            name=group_data.name,
            description=group_data.description,
            default_currency=group_data.default_currency,
            invite_code=invite_code,
            created_by=creator_id,
        )
        
        db.add(db_group)
        await db.flush()  # Get the group ID
        
        # Add creator as admin member
        db_member = GroupMember(
            group_id=db_group.id,
            user_id=creator_id,
            role="admin"
        )
        
        db.add(db_member)
        await db.commit()
        await db.refresh(db_group)
        
        return db_group
    
    @staticmethod
    async def get_user_groups(user_id: uuid.UUID, db: AsyncSession) -> List[Group]:
        """
        Get all groups for a user
        
        Args:
            user_id: User ID
            db: Database session
            
        Returns:
            List of groups the user is a member of
        """
        result = await db.execute(
            select(Group)
            .join(GroupMember)
            .where(
                and_(
                    GroupMember.user_id == user_id,
                    Group.is_active == True
                )
            )
            .order_by(Group.created_at.desc())
        )
        
        return result.scalars().all()
    
    @staticmethod
    async def get_group_by_id(group_id: uuid.UUID, user_id: uuid.UUID, db: AsyncSession) -> Optional[Group]:
        """
        Get group by ID if user is a member
        
        Args:
            group_id: Group ID
            user_id: User ID
            db: Database session
            
        Returns:
            Group object if user is a member, None otherwise
        """
        result = await db.execute(
            select(Group)
            .join(GroupMember)
            .where(
                and_(
                    Group.id == group_id,
                    GroupMember.user_id == user_id,
                    Group.is_active == True
                )
            )
        )
        
        return result.scalar_one_or_none()
    
    @staticmethod
    async def update_group(group_id: uuid.UUID, group_data: GroupUpdate, user_id: uuid.UUID, db: AsyncSession) -> Group:
        """
        Update group (admin only)
        
        Args:
            group_id: Group ID
            group_data: Update data
            user_id: User ID
            db: Database session
            
        Returns:
            Updated group object
            
        Raises:
            HTTPException: If user is not admin or group not found
        """
        # Check if user is admin of the group
        result = await db.execute(
            select(Group)
            .join(GroupMember)
            .where(
                and_(
                    Group.id == group_id,
                    GroupMember.user_id == user_id,
                    GroupMember.role == "admin",
                    Group.is_active == True
                )
            )
        )
        
        group = result.scalar_one_or_none()
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Group not found or insufficient permissions"
            )
        
        # Update group fields
        update_dict = group_data.dict(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(group, field, value)
        
        group.updated_at = datetime.utcnow()
        await db.commit()
        await db.refresh(group)
        
        return group

    @staticmethod
    async def delete_group(group_id: uuid.UUID, user_id: uuid.UUID, db: AsyncSession) -> bool:
        """
        Delete group (admin only)

        Args:
            group_id: Group ID
            user_id: User ID
            db: Database session

        Returns:
            True if successfully deleted

        Raises:
            HTTPException: If user is not admin or group not found
        """
        # Check if user is admin of the group
        result = await db.execute(
            select(Group)
            .join(GroupMember)
            .where(
                and_(
                    Group.id == group_id,
                    GroupMember.user_id == user_id,
                    GroupMember.role == "admin",
                    Group.is_active == True
                )
            )
        )

        group = result.scalar_one_or_none()
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Group not found or insufficient permissions"
            )

        # Soft delete the group
        group.is_active = False
        group.updated_at = datetime.utcnow()
        await db.commit()

        return True

    @staticmethod
    async def join_group_by_invite(invite_code: str, user_id: uuid.UUID, db: AsyncSession) -> Group:
        """
        Join group using invite code
        
        Args:
            invite_code: Group invite code
            user_id: User ID
            db: Database session
            
        Returns:
            Group object
            
        Raises:
            HTTPException: If invite code is invalid or user already member
        """
        # Find group by invite code
        result = await db.execute(
            select(Group).where(
                and_(
                    Group.invite_code == invite_code,
                    Group.is_active == True
                )
            )
        )
        
        group = result.scalar_one_or_none()
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invalid invite code"
            )
        
        # Check if user is already a member
        result = await db.execute(
            select(GroupMember).where(
                and_(
                    GroupMember.group_id == group.id,
                    GroupMember.user_id == user_id
                )
            )
        )
        
        existing_member = result.scalar_one_or_none()
        if existing_member:
            # User is already a member, just return the group
            return group
        
        # Add user as member
        db_member = GroupMember(
            group_id=group.id,
            user_id=user_id,
            role="member"
        )
        
        db.add(db_member)
        await db.commit()
        await db.refresh(group)
        
        return group
    
    @staticmethod
    async def leave_group(group_id: uuid.UUID, user_id: uuid.UUID, db: AsyncSession) -> bool:
        """
        Leave group
        
        Args:
            group_id: Group ID
            user_id: User ID
            db: Database session
            
        Returns:
            True if successfully left
            
        Raises:
            HTTPException: If user is not member or is the only admin
        """
        # Get user's membership
        result = await db.execute(
            select(GroupMember).where(
                and_(
                    GroupMember.group_id == group_id,
                    GroupMember.user_id == user_id
                )
            )
        )
        
        membership = result.scalar_one_or_none()
        if not membership:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="You are not a member of this group"
            )
        
        # If user is admin, check if there are other admins
        if membership.role == "admin":
            result = await db.execute(
                select(func.count(GroupMember.id)).where(
                    and_(
                        GroupMember.group_id == group_id,
                        GroupMember.role == "admin",
                        GroupMember.user_id != user_id
                    )
                )
            )
            
            admin_count = result.scalar()
            if admin_count == 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Cannot leave group: you are the only admin. Transfer admin rights first or delete the group."
                )
        
        # Remove membership
        await db.delete(membership)
        await db.commit()
        
        return True
    
    @staticmethod
    async def generate_qr_code(invite_link: str) -> str:
        """
        Generate QR code for invite link
        
        Args:
            invite_link: Invite link URL
            
        Returns:
            Base64 encoded QR code image
        """
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(invite_link)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Convert to base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{img_str}"
    
    @staticmethod
    async def regenerate_invite_code(group_id: uuid.UUID, user_id: uuid.UUID, db: AsyncSession) -> str:
        """
        Regenerate invite code for group (admin only)
        
        Args:
            group_id: Group ID
            user_id: User ID
            db: Database session
            
        Returns:
            New invite code
            
        Raises:
            HTTPException: If user is not admin
        """
        # Check if user is admin
        result = await db.execute(
            select(Group)
            .join(GroupMember)
            .where(
                and_(
                    Group.id == group_id,
                    GroupMember.user_id == user_id,
                    GroupMember.role == "admin"
                )
            )
        )
        
        group = result.scalar_one_or_none()
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Group not found or insufficient permissions"
            )
        
        # Generate new unique invite code
        new_invite_code = generate_invite_code()
        while True:
            result = await db.execute(
                select(Group).where(Group.invite_code == new_invite_code)
            )
            if not result.scalar_one_or_none():
                break
            new_invite_code = generate_invite_code()
        
        group.invite_code = new_invite_code
        group.updated_at = datetime.utcnow()
        await db.commit()
        
        return new_invite_code

    @staticmethod
    async def get_group_members(group_id: uuid.UUID, user_id: uuid.UUID, db: AsyncSession) -> List[GroupMember]:
        """
        Get all members of a group

        Args:
            group_id: Group ID
            user_id: User ID (must be member)
            db: Database session

        Returns:
            List of group members

        Raises:
            HTTPException: If user is not a member
        """
        # Check if user is member
        result = await db.execute(
            select(GroupMember).where(
                and_(
                    GroupMember.group_id == group_id,
                    GroupMember.user_id == user_id
                )
            )
        )

        if not result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Group not found or you are not a member"
            )

        # Get all members with user details
        result = await db.execute(
            select(GroupMember)
            .join(User)
            .where(GroupMember.group_id == group_id)
            .order_by(GroupMember.joined_at)
        )

        return result.scalars().all()
