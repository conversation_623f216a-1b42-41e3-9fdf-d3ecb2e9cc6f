"""
Test script to verify currency handling in the receipt scanning flow
"""

import requests
import json
from PIL import Image, ImageDraw, ImageFont
import io

def create_czech_receipt_image():
    """Create a mock Czech receipt image with CZK currency"""
    # Create a white image
    img = Image.new('RGB', (400, 600), color='white')
    draw = ImageDraw.Draw(img)
    
    # Try to use a default font, fallback to basic if not available
    try:
        font = ImageFont.truetype("arial.ttf", 16)
        small_font = ImageFont.truetype("arial.ttf", 12)
    except:
        font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    # Draw receipt content
    y = 20
    draw.text((20, y), "RESTAURACE U FLEKU", fill='black', font=font)
    y += 30
    draw.text((20, y), "Křemencova 11, Praha 1", fill='black', font=small_font)
    y += 25
    draw.text((20, y), "Tel: +420 224 934 019", fill='black', font=small_font)
    y += 40
    
    draw.text((20, y), "Datum: 22.06.2024", fill='black', font=small_font)
    draw.text((250, y), "Čas: 19:30", fill='black', font=small_font)
    y += 40
    
    # Items
    draw.text((20, y), "Pivo Pilsner Urquell 0,5l", fill='black', font=small_font)
    draw.text((300, y), "85 Kč", fill='black', font=small_font)
    y += 25
    
    draw.text((20, y), "Svíčková na smetaně", fill='black', font=small_font)
    draw.text((300, y), "245 Kč", fill='black', font=small_font)
    y += 25
    
    draw.text((20, y), "Knedlík", fill='black', font=small_font)
    draw.text((300, y), "35 Kč", fill='black', font=small_font)
    y += 40
    
    # Total
    draw.text((20, y), "CELKEM:", fill='black', font=font)
    draw.text((300, y), "365 Kč", fill='black', font=font)
    
    # Convert to bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    return img_bytes.getvalue()

def test_currency_detection():
    """Test if the API correctly detects CZK currency"""
    try:
        # Create test Czech receipt
        image_data = create_czech_receipt_image()
        
        # Test the API
        files = {'file': ('czech_receipt.jpg', image_data, 'image/jpeg')}
        data = {'translation_language': 'English'}
        
        print("🔄 Testing Czech receipt scanning...")
        response = requests.post(
            "http://localhost:8000/scan-receipt-upload",
            files=files,
            data=data,
            timeout=60
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API call successful!")
            print(f"Success: {result.get('success')}")
            
            if result.get('data'):
                data = result['data']
                print(f"\nExtracted Data:")
                print(f"Merchant: {data.get('merchant_name', 'N/A')}")
                print(f"Totals: {data.get('totals', [])}")
                print(f"Items: {len(data.get('itemized_list', []))}")
                
                # Check if CZK currency is detected
                totals = data.get('totals', [])
                if totals:
                    for total in totals:
                        currency = total.get('currency', 'N/A')
                        amount = total.get('amount', 'N/A')
                        print(f"💰 Total: {amount} {currency}")
                        
                        if currency == 'CZK':
                            print("✅ CZK currency correctly detected!")
                        else:
                            print(f"⚠️ Expected CZK but got {currency}")
                else:
                    print("⚠️ No totals found")
                    
                # Check items
                items = data.get('itemized_list', [])
                if items:
                    print(f"\nItems:")
                    for item in items:
                        name = item.get('item_name', 'N/A')
                        translation = item.get('translation', 'N/A')
                        price = item.get('price', 'N/A')
                        currency = item.get('currency', 'N/A')
                        print(f"  • {name} ({translation}): {price} {currency}")
            else:
                print("⚠️ No data returned")
        else:
            print(f"❌ API call failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_currency_detection()
