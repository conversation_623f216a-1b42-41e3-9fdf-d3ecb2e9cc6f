import React, { useState } from 'react';
import { ChevronDown, ChevronUp, User, Calendar, MessageSquare, Receipt, Users, Paperclip, FileText, Image } from 'lucide-react';
import { Expense } from '../types';
import AttachmentViewer from './AttachmentViewer';

interface ExpenseCardProps {
  expense: Expense;
  onClick?: () => void;
}

const ExpenseCard: React.FC<ExpenseCardProps> = ({ expense, onClick }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [viewerFile, setViewerFile] = useState<string | null>(null);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  };

  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't expand if clicking on the expand button
    if ((e.target as HTMLElement).closest('.expand-button')) {
      return;
    }
    
    if (onClick) {
      onClick();
    } else {
      setIsExpanded(!isExpanded);
    }
  };

  const handleExpandClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:shadow-md transition-shadow">
      {/* Mobile-First Card Content */}
      <div
        className="p-4 cursor-pointer"
        onClick={handleCardClick}
      >
        {/* Header with title and amount */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center flex-shrink-0">
              <Receipt className="h-6 w-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-base font-semibold text-gray-900 dark:text-white truncate">
                {expense.description}
              </h3>
              <div className="text-lg font-bold text-primary-600 dark:text-primary-400 mt-1">
                {formatCurrency(expense.total_amount, expense.currency)}
              </div>
            </div>
          </div>

          <button
            onClick={handleExpandClick}
            className="expand-button p-2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors ml-2"
          >
            {isExpanded ? (
              <ChevronUp className="h-6 w-6" />
            ) : (
              <ChevronDown className="h-6 w-6" />
            )}
          </button>
        </div>

        {/* Mobile info row */}
        <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
          <div className="flex items-center space-x-1">
            <User className="h-4 w-4" />
            <span className="truncate max-w-24">{expense.payer_name || 'Unknown'}</span>
          </div>

          <div className="flex items-center space-x-1">
            <Calendar className="h-4 w-4" />
            <span>{formatDate(expense.date)}</span>
          </div>

          {expense.items.length > 0 && (
            <div className="flex items-center space-x-1">
              <Receipt className="h-4 w-4" />
              <span>{expense.items.length}</span>
            </div>
          )}
        </div>

        {/* Quick indicators */}
        <div className="flex items-center space-x-3 mt-2">
          {expense.comment && (
            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
              <MessageSquare className="h-3 w-3 mr-1" />
              <span className="truncate max-w-20">
                {truncateText(expense.comment, 15)}
              </span>
            </div>
          )}

          {expense.attachments && expense.attachments.length > 0 && (
            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
              <Paperclip className="h-3 w-3 mr-1" />
              <span>{expense.attachments.length}</span>
            </div>
          )}
        </div>
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <div className="p-4 space-y-4">
            {/* Full Comment */}
            {expense.comment && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Comment
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-300 bg-white dark:bg-gray-800 p-3 rounded border border-gray-200 dark:border-gray-600">
                  {expense.comment}
                </p>
              </div>
            )}

            {/* Items */}
            {expense.items.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                  <Receipt className="h-4 w-4 mr-2" />
                  Items ({expense.items.length})
                </h4>
                <div className="space-y-2">
                  {expense.items.map((item, index) => (
                    <div key={index} className="flex justify-between items-center bg-white dark:bg-gray-800 p-2 rounded border border-gray-200 dark:border-gray-600">
                      <div className="flex-1">
                        <span className="text-sm text-gray-900 dark:text-white">{item.name}</span>
                        {item.quantity > 1 && (
                          <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">×{item.quantity}</span>
                        )}
                      </div>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {formatCurrency(item.price * item.quantity, expense.currency)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Splits */}
            {expense.splits.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                  <Users className="h-4 w-4 mr-2" />
                  Split ({expense.splits.length} people)
                </h4>
                <div className="space-y-2">
                  {expense.splits.map((split, index) => (
                    <div key={index} className="flex justify-between items-center bg-white dark:bg-gray-800 p-2 rounded border border-gray-200 dark:border-gray-600">
                      <span className="text-sm text-gray-900 dark:text-white">{split.user_name}</span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {formatCurrency(split.amount, expense.currency)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Attachments */}
            {expense.attachments && expense.attachments.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                  <Paperclip className="h-4 w-4 mr-2" />
                  Attachments ({expense.attachments.length})
                </h4>
                <div className="space-y-2">
                  {expense.attachments.map((filename, index) => {
                    const fileExtension = filename.split('.').pop()?.toLowerCase();
                    const isPDF = fileExtension === 'pdf';
                    const isImage = ['jpg', 'jpeg', 'png'].includes(fileExtension || '');

                    return (
                      <div key={index} className="flex items-center justify-between bg-white dark:bg-gray-800 p-2 rounded border border-gray-200 dark:border-gray-600">
                        <div className="flex items-center space-x-3 flex-1 min-w-0">
                          {isPDF ? (
                            <FileText className="h-5 w-5 text-red-500 dark:text-red-400" />
                          ) : isImage ? (
                            <Image className="h-5 w-5 text-blue-500 dark:text-blue-400" />
                          ) : (
                            <Paperclip className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                          )}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                              {filename}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {fileExtension?.toUpperCase()} file
                            </p>
                          </div>
                        </div>

                        <button
                          onClick={() => setViewerFile(filename)}
                          className="px-3 py-1 text-sm text-primary-600 hover:text-primary-700 hover:bg-primary-50 rounded transition-colors"
                        >
                          View
                        </button>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Metadata */}
            <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
              <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>Created: {formatDate(expense.created_at)}</span>
                <span>ID: {expense.id.substring(0, 8)}...</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Attachment Viewer */}
      {viewerFile && (
        <AttachmentViewer
          filename={viewerFile}
          isOpen={!!viewerFile}
          onClose={() => setViewerFile(null)}
        />
      )}
    </div>
  );
};

export default ExpenseCard;
