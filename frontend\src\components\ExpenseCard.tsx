import React, { useState } from 'react';
import { ChevronDown, ChevronUp, User, Calendar, MessageSquare, Receipt, Users, Paperclip, FileText, Image } from 'lucide-react';
import { Expense } from '../types';
import AttachmentViewer from './AttachmentViewer';

interface ExpenseCardProps {
  expense: Expense;
  onClick?: () => void;
}

const ExpenseCard: React.FC<ExpenseCardProps> = ({ expense, onClick }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [viewerFile, setViewerFile] = useState<string | null>(null);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  };

  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't expand if clicking on the expand button
    if ((e.target as HTMLElement).closest('.expand-button')) {
      return;
    }
    
    if (onClick) {
      onClick();
    } else {
      setIsExpanded(!isExpanded);
    }
  };

  const handleExpandClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:shadow-md transition-shadow">
      {/* Main Card Content */}
      <div
        className="p-4 cursor-pointer"
        onClick={handleCardClick}
      >
        <div className="flex items-center justify-between">
          {/* Left side - Expense info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                  <Receipt className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                </div>
              </div>

              <div className="flex-1 min-w-0">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {expense.description}
                </h3>
                
                <div className="flex items-center space-x-4 mt-1">
                  <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                    <User className="h-3 w-3 mr-1" />
                    {expense.payer_name || 'Unknown'}
                  </div>

                  <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                    <Calendar className="h-3 w-3 mr-1" />
                    {formatDate(expense.date)}
                  </div>

                  {expense.comment && (
                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                      <MessageSquare className="h-3 w-3 mr-1" />
                      <span className="truncate max-w-32">
                        {truncateText(expense.comment, 20)}
                      </span>
                    </div>
                  )}

                  {expense.attachments && expense.attachments.length > 0 && (
                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                      <Paperclip className="h-3 w-3 mr-1" />
                      <span>
                        {expense.attachments.length} attachment{expense.attachments.length !== 1 ? 's' : ''}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Amount and expand button */}
          <div className="flex items-center space-x-3">
            <div className="text-right">
              <div className="text-lg font-semibold text-gray-900 dark:text-white">
                {formatCurrency(expense.total_amount, expense.currency)}
              </div>
              {expense.items.length > 0 && (
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {expense.items.length} item{expense.items.length !== 1 ? 's' : ''}
                </div>
              )}
            </div>
            
            <button
              onClick={handleExpandClick}
              className="expand-button p-1 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              {isExpanded ? (
                <ChevronUp className="h-5 w-5" />
              ) : (
                <ChevronDown className="h-5 w-5" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <div className="p-4 space-y-4">
            {/* Full Comment */}
            {expense.comment && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Comment
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-300 bg-white dark:bg-gray-800 p-3 rounded border border-gray-200 dark:border-gray-600">
                  {expense.comment}
                </p>
              </div>
            )}

            {/* Items */}
            {expense.items.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                  <Receipt className="h-4 w-4 mr-2" />
                  Items ({expense.items.length})
                </h4>
                <div className="space-y-2">
                  {expense.items.map((item, index) => (
                    <div key={index} className="flex justify-between items-center bg-white dark:bg-gray-800 p-2 rounded border border-gray-200 dark:border-gray-600">
                      <div className="flex-1">
                        <span className="text-sm text-gray-900 dark:text-white">{item.name}</span>
                        {item.quantity > 1 && (
                          <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">×{item.quantity}</span>
                        )}
                      </div>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {formatCurrency(item.price * item.quantity, expense.currency)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Splits */}
            {expense.splits.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                  <Users className="h-4 w-4 mr-2" />
                  Split ({expense.splits.length} people)
                </h4>
                <div className="space-y-2">
                  {expense.splits.map((split, index) => (
                    <div key={index} className="flex justify-between items-center bg-white dark:bg-gray-800 p-2 rounded border border-gray-200 dark:border-gray-600">
                      <span className="text-sm text-gray-900 dark:text-white">{split.user_name}</span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {formatCurrency(split.amount, expense.currency)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Attachments */}
            {expense.attachments && expense.attachments.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                  <Paperclip className="h-4 w-4 mr-2" />
                  Attachments ({expense.attachments.length})
                </h4>
                <div className="space-y-2">
                  {expense.attachments.map((filename, index) => {
                    const fileExtension = filename.split('.').pop()?.toLowerCase();
                    const isPDF = fileExtension === 'pdf';
                    const isImage = ['jpg', 'jpeg', 'png'].includes(fileExtension || '');

                    return (
                      <div key={index} className="flex items-center justify-between bg-white dark:bg-gray-800 p-2 rounded border border-gray-200 dark:border-gray-600">
                        <div className="flex items-center space-x-3 flex-1 min-w-0">
                          {isPDF ? (
                            <FileText className="h-5 w-5 text-red-500 dark:text-red-400" />
                          ) : isImage ? (
                            <Image className="h-5 w-5 text-blue-500 dark:text-blue-400" />
                          ) : (
                            <Paperclip className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                          )}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                              {filename}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {fileExtension?.toUpperCase()} file
                            </p>
                          </div>
                        </div>

                        <button
                          onClick={() => setViewerFile(filename)}
                          className="px-3 py-1 text-sm text-primary-600 hover:text-primary-700 hover:bg-primary-50 rounded transition-colors"
                        >
                          View
                        </button>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Metadata */}
            <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
              <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>Created: {formatDate(expense.created_at)}</span>
                <span>ID: {expense.id.substring(0, 8)}...</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Attachment Viewer */}
      {viewerFile && (
        <AttachmentViewer
          filename={viewerFile}
          isOpen={!!viewerFile}
          onClose={() => setViewerFile(null)}
        />
      )}
    </div>
  );
};

export default ExpenseCard;
