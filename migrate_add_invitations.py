"""
Migration script to add group_invitations table
"""

import sqlite3
import os

def migrate_database():
    db_path = "easysplit.db"
    
    if not os.path.exists(db_path):
        print("Database file not found!")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check if group_invitations table already exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='group_invitations'")
        if cursor.fetchone():
            print("group_invitations table already exists!")
            return
        
        print("Creating group_invitations table...")
        
        # Create the group_invitations table
        cursor.execute("""
            CREATE TABLE group_invitations (
                id TEXT PRIMARY KEY,
                group_id TEXT NOT NULL,
                inviter_id TEXT NOT NULL,
                invitee_id TEXT,
                invitee_email TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                message TEXT,
                expires_at TIMESTAMP,
                responded_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (group_id) REFERENCES groups (id) ON DELETE CASCADE,
                FOREIGN KEY (inviter_id) REFERENCES users (id),
                FOREIGN KEY (invitee_id) REFERENCES users (id)
            )
        """)
        
        conn.commit()
        print("Migration completed successfully!")
        
    except Exception as e:
        print(f"Migration failed: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    migrate_database()
