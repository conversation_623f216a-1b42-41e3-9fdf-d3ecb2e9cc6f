"""
Database models for EasySplit application
"""

import uuid
from datetime import datetime
from sqlalchemy import (
    Column, String, Boolean, DateTime, Text, Integer, 
    Numeric, Date, ForeignKey, UniqueConstraint
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.dialects.sqlite import CHAR
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.types import TypeDecorator

# Custom UUID type for SQLite compatibility
class GUID(TypeDecorator):
    impl = CHAR
    cache_ok = True

    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(UUID())
        else:
            return dialect.type_descriptor(CHAR(32))

    def process_bind_param(self, value, dialect):
        if value is None:
            return value
        elif dialect.name == 'postgresql':
            return str(value)
        else:
            if not isinstance(value, uuid.UUID):
                return "%.32x" % uuid.UUID(value).int
            else:
                return "%.32x" % value.int

    def process_result_value(self, value, dialect):
        if value is None:
            return value
        else:
            if not isinstance(value, uuid.UUID):
                return uuid.UUID(value)
            else:
                return value

Base = declarative_base()

class User(Base):
    """User model for authentication and profile management"""
    __tablename__ = "users"

    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, nullable=False, index=True)
    username = Column(String(50), unique=True, nullable=True, index=True)
    full_name = Column(String(255), nullable=True)
    password_hash = Column(String(255), nullable=False)
    avatar_url = Column(Text, nullable=True)
    phone = Column(String(20), nullable=True)
    preferred_currency = Column(String(3), default="USD")
    timezone = Column(String(50), default="UTC")
    
    # Security fields
    two_factor_enabled = Column(Boolean, default=False)
    two_factor_secret = Column(String(255), nullable=True)
    email_verified = Column(Boolean, default=False)
    email_verification_token = Column(String(255), nullable=True)
    password_reset_token = Column(String(255), nullable=True)
    password_reset_expires = Column(DateTime, nullable=True)
    
    # Status fields
    is_active = Column(Boolean, default=True)
    last_login = Column(DateTime, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<User(id={self.id}, email={self.email}, username={self.username})>"

class RefreshToken(Base):
    """Refresh token model for JWT token management"""
    __tablename__ = "refresh_tokens"

    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    user_id = Column(GUID(), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    token_hash = Column(String(255), nullable=False, unique=True)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    is_revoked = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User")

    def __repr__(self):
        return f"<RefreshToken(id={self.id}, user_id={self.user_id}, expires_at={self.expires_at})>"

class Group(Base):
    """Group model for expense sharing groups"""
    __tablename__ = "groups"

    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    avatar_url = Column(Text, nullable=True)
    invite_code = Column(String(10), unique=True, nullable=True, index=True)
    default_currency = Column(String(3), default="USD")
    created_by = Column(GUID(), ForeignKey("users.id"), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    creator = relationship("User", foreign_keys=[created_by])
    members = relationship("GroupMember", back_populates="group")

    def __repr__(self):
        return f"<Group(id={self.id}, name={self.name}, created_by={self.created_by})>"

class GroupMember(Base):
    """Group membership model"""
    __tablename__ = "group_members"

    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    group_id = Column(GUID(), ForeignKey("groups.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(GUID(), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    role = Column(String(20), default="member")  # 'admin', 'member'
    joined_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    group = relationship("Group", back_populates="members")
    user = relationship("User")

    # Constraints
    __table_args__ = (
        UniqueConstraint('group_id', 'user_id', name='unique_group_user'),
    )

    def __repr__(self):
        return f"<GroupMember(group_id={self.group_id}, user_id={self.user_id}, role={self.role})>"

class Expense(Base):
    """Expense model for tracking shared expenses"""
    __tablename__ = "expenses"

    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    group_id = Column(GUID(), ForeignKey("groups.id", ondelete="CASCADE"), nullable=False)
    created_by = Column(GUID(), ForeignKey("users.id"), nullable=False)
    payer_id = Column(GUID(), ForeignKey("users.id"), nullable=False)  # Who actually paid for the expense
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    total_amount = Column(Numeric(12, 2), nullable=False)
    currency = Column(String(3), nullable=False)
    category = Column(String(50), nullable=True)
    expense_date = Column(Date, nullable=False)
    receipt_url = Column(Text, nullable=True)
    receipt_data = Column(Text, nullable=True)  # JSON data
    split_method = Column(String(20), default="equal")  # 'equal', 'exact', 'percentage'
    is_settled = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    group = relationship("Group")
    creator = relationship("User", foreign_keys=[created_by])
    payer = relationship("User", foreign_keys=[payer_id])

    def __repr__(self):
        return f"<Expense(id={self.id}, title={self.title}, amount={self.total_amount})>"

class ExpenseSplit(Base):
    """Expense split model for tracking who owes what"""
    __tablename__ = "expense_splits"

    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    expense_id = Column(GUID(), ForeignKey("expenses.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(GUID(), ForeignKey("users.id"), nullable=False)
    amount = Column(Numeric(12, 2), nullable=False)
    percentage = Column(Numeric(5, 2), nullable=True)  # for percentage splits
    is_paid = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    expense = relationship("Expense")
    user = relationship("User")

    def __repr__(self):
        return f"<ExpenseSplit(expense_id={self.expense_id}, user_id={self.user_id}, amount={self.amount})>"

class ExpenseItem(Base):
    """Expense item model for itemized expenses"""
    __tablename__ = "expense_items"

    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    expense_id = Column(GUID(), ForeignKey("expenses.id", ondelete="CASCADE"), nullable=False)
    name = Column(String(255), nullable=False)
    price = Column(Numeric(12, 2), nullable=False)
    quantity = Column(Integer, nullable=False, default=1)
    assigned_to = Column(Text, nullable=True)  # JSON array of user IDs
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    expense = relationship("Expense")

    def __repr__(self):
        return f"<ExpenseItem(id={self.id}, name={self.name}, price={self.price})>"

class Settlement(Base):
    """Settlement model for tracking payments between users"""
    __tablename__ = "settlements"

    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    group_id = Column(GUID(), ForeignKey("groups.id"), nullable=False)
    payer_id = Column(GUID(), ForeignKey("users.id"), nullable=False)
    payee_id = Column(GUID(), ForeignKey("users.id"), nullable=False)
    amount = Column(Numeric(12, 2), nullable=False)
    currency = Column(String(3), nullable=False)
    payment_method = Column(String(50), nullable=True)  # 'cash', 'venmo', 'paypal', etc.
    payment_reference = Column(String(255), nullable=True)  # external payment ID
    status = Column(String(20), default="pending")  # 'pending', 'confirmed', 'disputed'
    notes = Column(Text, nullable=True)
    confirmed_by_payee = Column(Boolean, default=False)
    confirmed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    group = relationship("Group")
    payer = relationship("User", foreign_keys=[payer_id])
    payee = relationship("User", foreign_keys=[payee_id])

    def __repr__(self):
        return f"<Settlement(id={self.id}, payer_id={self.payer_id}, payee_id={self.payee_id}, amount={self.amount})>"

class GroupInvitation(Base):
    """Group invitation model for tracking pending invitations"""
    __tablename__ = "group_invitations"

    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    group_id = Column(GUID(), ForeignKey("groups.id", ondelete="CASCADE"), nullable=False)
    inviter_id = Column(GUID(), ForeignKey("users.id"), nullable=False)
    invitee_id = Column(GUID(), ForeignKey("users.id"), nullable=False)
    invitee_email = Column(String(255), nullable=False)  # Store email for non-registered users
    status = Column(String(20), default="pending")  # 'pending', 'accepted', 'declined', 'expired'
    message = Column(Text, nullable=True)  # Optional invitation message
    expires_at = Column(DateTime(timezone=True), nullable=True)  # Optional expiration
    responded_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    group = relationship("Group")
    inviter = relationship("User", foreign_keys=[inviter_id])
    invitee = relationship("User", foreign_keys=[invitee_id])

    def __repr__(self):
        return f"<GroupInvitation(id={self.id}, group_id={self.group_id}, invitee_email={self.invitee_email}, status={self.status})>"
