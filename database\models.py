"""
Database models for EasySplit application
"""

import uuid
from datetime import datetime
from typing import Optional, List
from sqlalchemy import (
    Column, String, Boolean, DateTime, Text, Integer,
    Numeric, Date, ForeignKey, UniqueConstraint
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.dialects.sqlite import CHAR
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.types import TypeDecorator, CHAR

Base = declarative_base()

class User(Base):
    """User model for authentication and profile management"""
    __tablename__ = "users"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid.uuid4
    )
    email: Mapped[str] = mapped_column(
        String(255), 
        unique=True, 
        nullable=False, 
        index=True
    )
    username: Mapped[Optional[str]] = mapped_column(
        String(50), 
        unique=True, 
        nullable=True,
        index=True
    )
    full_name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    password_hash: Mapped[str] = mapped_column(String(255), nullable=False)
    avatar_url: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    phone: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    preferred_currency: Mapped[str] = mapped_column(String(3), default="USD")
    timezone: Mapped[str] = mapped_column(String(50), default="UTC")
    
    # Security fields
    two_factor_enabled: Mapped[bool] = mapped_column(Boolean, default=False)
    two_factor_secret: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    email_verified: Mapped[bool] = mapped_column(Boolean, default=False)
    email_verification_token: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    password_reset_token: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    password_reset_expires: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # Status fields
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    last_login: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        onupdate=func.now()
    )

    # Relationships
    created_groups: Mapped[List["Group"]] = relationship(
        "Group", 
        back_populates="creator",
        foreign_keys="Group.created_by"
    )
    group_memberships: Mapped[List["GroupMember"]] = relationship(
        "GroupMember", 
        back_populates="user"
    )
    created_expenses: Mapped[List["Expense"]] = relationship(
        "Expense", 
        back_populates="creator"
    )
    expense_splits: Mapped[List["ExpenseSplit"]] = relationship(
        "ExpenseSplit", 
        back_populates="user"
    )
    payments_made: Mapped[List["Settlement"]] = relationship(
        "Settlement", 
        back_populates="payer",
        foreign_keys="Settlement.payer_id"
    )
    payments_received: Mapped[List["Settlement"]] = relationship(
        "Settlement", 
        back_populates="payee",
        foreign_keys="Settlement.payee_id"
    )

    def __repr__(self):
        return f"<User(id={self.id}, email={self.email}, username={self.username})>"

class RefreshToken(Base):
    """Refresh token model for JWT token management"""
    __tablename__ = "refresh_tokens"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid.uuid4
    )
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False
    )
    token_hash: Mapped[str] = mapped_column(String(255), nullable=False, unique=True)
    expires_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    is_revoked: Mapped[bool] = mapped_column(Boolean, default=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now()
    )

    # Relationships
    user: Mapped["User"] = relationship("User")

    def __repr__(self):
        return f"<RefreshToken(id={self.id}, user_id={self.user_id}, expires_at={self.expires_at})>"

class Group(Base):
    """Group model for expense sharing groups"""
    __tablename__ = "groups"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid.uuid4
    )
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    avatar_url: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    invite_code: Mapped[Optional[str]] = mapped_column(
        String(10), 
        unique=True, 
        nullable=True,
        index=True
    )
    default_currency: Mapped[str] = mapped_column(String(3), default="USD")
    created_by: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        ForeignKey("users.id"),
        nullable=False
    )
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        onupdate=func.now()
    )

    # Relationships
    creator: Mapped["User"] = relationship(
        "User", 
        back_populates="created_groups",
        foreign_keys=[created_by]
    )
    members: Mapped[List["GroupMember"]] = relationship(
        "GroupMember", 
        back_populates="group"
    )
    expenses: Mapped[List["Expense"]] = relationship(
        "Expense", 
        back_populates="group"
    )
    settlements: Mapped[List["Settlement"]] = relationship(
        "Settlement", 
        back_populates="group"
    )

    def __repr__(self):
        return f"<Group(id={self.id}, name={self.name}, created_by={self.created_by})>"

class GroupMember(Base):
    """Group membership model"""
    __tablename__ = "group_members"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid.uuid4
    )
    group_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        ForeignKey("groups.id", ondelete="CASCADE"),
        nullable=False
    )
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False
    )
    role: Mapped[str] = mapped_column(String(20), default="member")  # 'admin', 'member'
    joined_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now()
    )

    # Relationships
    group: Mapped["Group"] = relationship("Group", back_populates="members")
    user: Mapped["User"] = relationship("User", back_populates="group_memberships")

    # Constraints
    __table_args__ = (
        UniqueConstraint('group_id', 'user_id', name='unique_group_user'),
    )

    def __repr__(self):
        return f"<GroupMember(group_id={self.group_id}, user_id={self.user_id}, role={self.role})>"

class Expense(Base):
    """Expense model for tracking shared expenses"""
    __tablename__ = "expenses"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    group_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("groups.id", ondelete="CASCADE"),
        nullable=False
    )
    created_by: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False
    )
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    total_amount: Mapped[float] = mapped_column(Numeric(12, 2), nullable=False)
    currency: Mapped[str] = mapped_column(String(3), nullable=False)
    category: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    expense_date: Mapped[datetime] = mapped_column(Date, nullable=False)
    receipt_url: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    receipt_data: Mapped[Optional[dict]] = mapped_column(Text, nullable=True)  # JSON data
    split_method: Mapped[str] = mapped_column(
        String(20),
        default="equal"
    )  # 'equal', 'exact', 'percentage'
    is_settled: Mapped[bool] = mapped_column(Boolean, default=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now()
    )

    # Relationships
    group: Mapped["Group"] = relationship("Group", back_populates="expenses")
    creator: Mapped["User"] = relationship("User", back_populates="created_expenses")
    splits: Mapped[List["ExpenseSplit"]] = relationship(
        "ExpenseSplit",
        back_populates="expense"
    )

    def __repr__(self):
        return f"<Expense(id={self.id}, title={self.title}, amount={self.total_amount})>"

class ExpenseSplit(Base):
    """Expense split model for tracking who owes what"""
    __tablename__ = "expense_splits"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    expense_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("expenses.id", ondelete="CASCADE"),
        nullable=False
    )
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False
    )
    amount: Mapped[float] = mapped_column(Numeric(12, 2), nullable=False)
    percentage: Mapped[Optional[float]] = mapped_column(
        Numeric(5, 2),
        nullable=True
    )  # for percentage splits
    is_paid: Mapped[bool] = mapped_column(Boolean, default=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )

    # Relationships
    expense: Mapped["Expense"] = relationship("Expense", back_populates="splits")
    user: Mapped["User"] = relationship("User", back_populates="expense_splits")

    def __repr__(self):
        return f"<ExpenseSplit(expense_id={self.expense_id}, user_id={self.user_id}, amount={self.amount})>"

class Settlement(Base):
    """Settlement model for tracking payments between users"""
    __tablename__ = "settlements"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    group_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("groups.id"),
        nullable=False
    )
    payer_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False
    )
    payee_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False
    )
    amount: Mapped[float] = mapped_column(Numeric(12, 2), nullable=False)
    currency: Mapped[str] = mapped_column(String(3), nullable=False)
    payment_method: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True
    )  # 'cash', 'venmo', 'paypal', etc.
    payment_reference: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True
    )  # external payment ID
    status: Mapped[str] = mapped_column(
        String(20),
        default="pending"
    )  # 'pending', 'confirmed', 'disputed'
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    confirmed_by_payee: Mapped[bool] = mapped_column(Boolean, default=False)
    confirmed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )

    # Relationships
    group: Mapped["Group"] = relationship("Group", back_populates="settlements")
    payer: Mapped["User"] = relationship(
        "User",
        back_populates="payments_made",
        foreign_keys=[payer_id]
    )
    payee: Mapped["User"] = relationship(
        "User",
        back_populates="payments_received",
        foreign_keys=[payee_id]
    )

    def __repr__(self):
        return f"<Settlement(id={self.id}, payer_id={self.payer_id}, payee_id={self.payee_id}, amount={self.amount})>"
