"""
Simple test to check if the receipt scanning API works with a minimal image
"""

import requests
import base64
from PIL import Image
import io

def create_test_image():
    """Create a simple test image"""
    # Create a simple white image with some text
    img = Image.new('RGB', (200, 100), color='white')
    
    # Convert to bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    return img_bytes.getvalue()

def test_receipt_api():
    try:
        # Create a test image
        image_data = create_test_image()
        
        # Test the API
        files = {'file': ('test.jpg', image_data, 'image/jpeg')}
        data = {'translation_language': 'English'}
        
        print("🔄 Testing receipt scanning API with test image...")
        response = requests.post(
            "http://localhost:8000/scan-receipt-upload",
            files=files,
            data=data,
            timeout=60
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API call successful!")
            print(f"Success: {result.get('success')}")
            if result.get('error'):
                print(f"Error: {result.get('error')}")
        else:
            print(f"❌ API call failed")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_receipt_api()
