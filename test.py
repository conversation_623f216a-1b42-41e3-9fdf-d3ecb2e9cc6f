import requests
import os
from dotenv import load_dotenv

# Load environment variables from .env
load_dotenv()

def get_api_key():
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key or api_key == "your_api_key_here":
        raise ValueError("Please set GEMINI_API_KEY in your .env file.")
    return api_key

def generate_content_with_image(prompt, image_base64, mime_type="image/jpeg"):
    """
    Sends a prompt and image to Gemini API and returns the generated content.
    """
    api_key = get_api_key()
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={api_key}"
    
    headers = {
        "Content-Type": "application/json"
    }

    payload = {
        "contents": [
            {
                "parts": [
                    {"text": prompt},
                    {
                        "inline_data": {
                            "mime_type": mime_type,
                            "data": image_base64
                        }
                    }
                ]
            }
        ]
    }

    try:
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error making API request: {e}")
        return None

def load_image_base64(image_path):
    import base64
    with open(image_path, "rb") as image_file:
        encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
    return encoded_string

if __name__ == "__main__":
    # Define prompt for receipt understanding
    language = "German"
    prompt = f"""You are a helpful assistant. Extract all useful information from the attached receipt image.
Do not convert any prices or currencies — extract and show them exactly as printed on the receipt.
If multiple currencies are present (e.g., items listed in USD and a final total in EUR), include both.

Return a structured JSON with the following fields:
- merchant_name
- merchant_address
- date (format: dd.mm.yyyy)
- time (format: HH:mm:ss)
- totals (list of totals found, each with amount and currency, do not include tips)
- itemized_list (all items with name, translation to {language}, quantity, price, and currency if shown)

Ignore tips they will be added later.

Example format:

{{
  "merchant_name": "placeholder",
  "merchant_address": "placeholder",
  "date": "12.06.2024",
  "time": "18:45:00",
  "totals": [
    {{
      "amount": "45.00",
      "currency": "USD"
    }},
    {{
      "amount": "41.00",
      "currency": "EUR"
    }}
  ],
  "translation_language": "{language}",
  "itemized_list": [
    {{
      "item_name": "Pizza tonno",
      "translation": "Thunfisch Pizza",
      "quantity": 1,
      "price": "15.00",
      "currency": "USD"
    }},
    {{
      "item_name": "Coca-Cola",
      "translation": "Coca-Cola",
      "quantity": 2,
      "price": "3.50",
      "currency": "USD"
    }}
  ]
}}
"""
    

    image_path = "r1.jpg"  # Replace with your image path
    image_data = load_image_base64(image_path)

    result = generate_content_with_image(prompt, image_data)
    if result:
        try:
            response = result["candidates"][0]["content"]["parts"][0]["text"]
            print(response)
        except Exception as e:
            print("Error parsing response:", e)



