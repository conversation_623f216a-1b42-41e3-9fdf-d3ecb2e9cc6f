import React, { useState, useEffect, useCallback } from 'react';
import { Eye, EyeOff, Mail, Lock, User, Phone, Loader2, AlertCircle, CheckCircle, X } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { authApi } from '../services/api';
import { RegisterRequest } from '../types';

interface RegisterFormProps {
  onSuccess?: () => void;
  onSwitchToLogin?: () => void;
}

const RegisterForm: React.FC<RegisterFormProps> = ({ onSuccess, onSwitchToLogin }) => {
  const { register, isLoading, error, clearError } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formData, setFormData] = useState<RegisterRequest>({
    email: '',
    password: '',
    confirm_password: '',
    username: '',
    full_name: '',
    phone: '',
    preferred_currency: 'USD',
    timezone: 'UTC',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [passwordStrength, setPasswordStrength] = useState<{
    score: number;
    feedback: string[];
  }>({ score: 0, feedback: [] });
  const [availability, setAvailability] = useState<{
    email: { available: boolean | null; checking: boolean };
    username: { available: boolean | null; checking: boolean };
  }>({
    email: { available: null, checking: false },
    username: { available: null, checking: false }
  });

  // Debounced availability checking
  const checkAvailability = useCallback(
    async (field: 'email' | 'username', value: string) => {
      if (!value || value.length < 3) {
        setAvailability(prev => ({
          ...prev,
          [field]: { available: null, checking: false }
        }));
        return;
      }

      setAvailability(prev => ({
        ...prev,
        [field]: { ...prev[field], checking: true }
      }));

      try {
        const params = field === 'email' ? { email: value } : { username: value };
        const result = await authApi.checkAvailability(
          params.email,
          params.username
        );

        const available = field === 'email'
          ? result.email_available
          : result.username_available;

        setAvailability(prev => ({
          ...prev,
          [field]: { available, checking: false }
        }));
      } catch (error) {
        setAvailability(prev => ({
          ...prev,
          [field]: { available: null, checking: false }
        }));
      }
    },
    []
  );

  // Debounce availability checking
  useEffect(() => {
    const timer = setTimeout(() => {
      if (formData.email) {
        checkAvailability('email', formData.email);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [formData.email, checkAvailability]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (formData.username) {
        checkAvailability('username', formData.username);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [formData.username, checkAvailability]);

  const validatePassword = (password: string) => {
    const feedback: string[] = [];
    let score = 0;

    if (password.length >= 8) score++;
    else feedback.push('At least 8 characters');

    if (/[A-Z]/.test(password)) score++;
    else feedback.push('One uppercase letter');

    if (/[a-z]/.test(password)) score++;
    else feedback.push('One lowercase letter');

    if (/\d/.test(password)) score++;
    else feedback.push('One number');

    if (/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) score++;
    else feedback.push('One special character');

    setPasswordStrength({ score, feedback });
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    } else if (availability.email.available === false) {
      errors.email = 'This email is already registered';
    }

    if (!formData.password) {
      errors.password = 'Password is required';
    } else if (passwordStrength.score < 4) {
      errors.password = 'Password does not meet requirements';
    }

    if (!formData.confirm_password) {
      errors.confirm_password = 'Please confirm your password';
    } else if (formData.password !== formData.confirm_password) {
      errors.confirm_password = 'Passwords do not match';
    }

    if (formData.username && formData.username.length < 3) {
      errors.username = 'Username must be at least 3 characters';
    } else if (formData.username && availability.username.available === false) {
      errors.username = 'This username is already taken';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();

    if (!validateForm()) {
      return;
    }

    try {
      await register(formData);
      onSuccess?.();
    } catch (error: any) {
      // Handle specific validation errors from backend
      const errorMessage = error.response?.data?.detail;

      if (errorMessage === "Email already registered") {
        setFormErrors(prev => ({
          ...prev,
          email: "This email is already registered. Please use a different email or try logging in."
        }));
      } else if (errorMessage === "Username already taken") {
        setFormErrors(prev => ({
          ...prev,
          username: "This username is already taken. Please choose a different username."
        }));
      }
      // Other errors are handled by the auth context
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Validate password strength on password change
    if (name === 'password') {
      validatePassword(value);
    }

    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const getPasswordStrengthColor = () => {
    if (passwordStrength.score <= 2) return 'bg-red-500';
    if (passwordStrength.score <= 3) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getPasswordStrengthText = () => {
    if (passwordStrength.score <= 2) return 'Weak';
    if (passwordStrength.score <= 3) return 'Medium';
    return 'Strong';
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-white rounded-lg shadow-md p-8">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900">Create Account</h2>
          <p className="text-gray-600 mt-2">Join EasySplit and start splitting expenses</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Email Field */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email Address *
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Mail className="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                value={formData.email}
                onChange={handleInputChange}
                className={`
                  block w-full pl-10 pr-10 py-2 border rounded-md shadow-sm placeholder-gray-400
                  focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent
                  ${formErrors.email ? 'border-red-300' :
                    availability.email.available === false ? 'border-red-300' :
                    availability.email.available === true ? 'border-green-300' : 'border-gray-300'}
                `}
                placeholder="Enter your email"
                disabled={isLoading}
              />

              {/* Availability indicator */}
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                {availability.email.checking ? (
                  <Loader2 className="h-4 w-4 text-gray-400 animate-spin" />
                ) : availability.email.available === true ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : availability.email.available === false ? (
                  <X className="h-4 w-4 text-red-500" />
                ) : null}
              </div>
            </div>

            {/* Email feedback */}
            {formErrors.email && (
              <p className="mt-1 text-sm text-red-600">{formErrors.email}</p>
            )}
            {!formErrors.email && availability.email.available === true && formData.email && (
              <p className="mt-1 text-sm text-green-600">✓ Email is available</p>
            )}
            {!formErrors.email && availability.email.available === false && formData.email && (
              <p className="mt-1 text-sm text-red-600">✗ Email is already registered</p>
            )}
          </div>

          {/* Username Field */}
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
              Username
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <User className="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="username"
                name="username"
                type="text"
                autoComplete="username"
                value={formData.username}
                onChange={handleInputChange}
                className={`
                  block w-full pl-10 pr-10 py-2 border rounded-md shadow-sm placeholder-gray-400
                  focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent
                  ${formErrors.username ? 'border-red-300' :
                    availability.username.available === false ? 'border-red-300' :
                    availability.username.available === true ? 'border-green-300' : 'border-gray-300'}
                `}
                placeholder="Choose a username (optional)"
                disabled={isLoading}
              />

              {/* Availability indicator */}
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                {availability.username.checking ? (
                  <Loader2 className="h-4 w-4 text-gray-400 animate-spin" />
                ) : availability.username.available === true && formData.username ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : availability.username.available === false ? (
                  <X className="h-4 w-4 text-red-500" />
                ) : null}
              </div>
            </div>

            {/* Username feedback */}
            {formErrors.username && (
              <p className="mt-1 text-sm text-red-600">{formErrors.username}</p>
            )}
            {!formErrors.username && availability.username.available === true && formData.username && (
              <p className="mt-1 text-sm text-green-600">✓ Username is available</p>
            )}
            {!formErrors.username && availability.username.available === false && formData.username && (
              <p className="mt-1 text-sm text-red-600">✗ Username is already taken</p>
            )}
          </div>

          {/* Full Name Field */}
          <div>
            <label htmlFor="full_name" className="block text-sm font-medium text-gray-700 mb-2">
              Full Name
            </label>
            <input
              id="full_name"
              name="full_name"
              type="text"
              autoComplete="name"
              value={formData.full_name}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Enter your full name (optional)"
              disabled={isLoading}
            />
          </div>

          {/* Password Field */}
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              Password *
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Lock className="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                autoComplete="new-password"
                value={formData.password}
                onChange={handleInputChange}
                className={`
                  block w-full pl-10 pr-10 py-2 border rounded-md shadow-sm placeholder-gray-400 
                  focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent
                  ${formErrors.password ? 'border-red-300' : 'border-gray-300'}
                `}
                placeholder="Create a strong password"
                disabled={isLoading}
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => setShowPassword(!showPassword)}
                disabled={isLoading}
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                ) : (
                  <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                )}
              </button>
            </div>
            
            {/* Password Strength Indicator */}
            {formData.password && (
              <div className="mt-2">
                <div className="flex items-center space-x-2">
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor()}`}
                      style={{ width: `${(passwordStrength.score / 5) * 100}%` }}
                    />
                  </div>
                  <span className="text-xs font-medium text-gray-600">
                    {getPasswordStrengthText()}
                  </span>
                </div>
                {passwordStrength.feedback.length > 0 && (
                  <div className="mt-1">
                    <p className="text-xs text-gray-600">Missing:</p>
                    <ul className="text-xs text-gray-500 list-disc list-inside">
                      {passwordStrength.feedback.map((item, index) => (
                        <li key={index}>{item}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
            
            {formErrors.password && (
              <p className="mt-1 text-sm text-red-600">{formErrors.password}</p>
            )}
          </div>

          {/* Confirm Password Field */}
          <div>
            <label htmlFor="confirm_password" className="block text-sm font-medium text-gray-700 mb-2">
              Confirm Password *
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Lock className="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="confirm_password"
                name="confirm_password"
                type={showConfirmPassword ? 'text' : 'password'}
                autoComplete="new-password"
                value={formData.confirm_password}
                onChange={handleInputChange}
                className={`
                  block w-full pl-10 pr-10 py-2 border rounded-md shadow-sm placeholder-gray-400 
                  focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent
                  ${formErrors.confirm_password ? 'border-red-300' : 'border-gray-300'}
                `}
                placeholder="Confirm your password"
                disabled={isLoading}
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                disabled={isLoading}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                ) : (
                  <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                )}
              </button>
            </div>
            
            {/* Password Match Indicator */}
            {formData.confirm_password && (
              <div className="mt-1 flex items-center">
                {formData.password === formData.confirm_password ? (
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="h-4 w-4 mr-1" />
                    <span className="text-xs">Passwords match</span>
                  </div>
                ) : (
                  <div className="flex items-center text-red-600">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    <span className="text-xs">Passwords do not match</span>
                  </div>
                )}
              </div>
            )}
            
            {formErrors.confirm_password && (
              <p className="mt-1 text-sm text-red-600">{formErrors.confirm_password}</p>
            )}
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-red-400 mr-2" />
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <>
                <Loader2 className="animate-spin h-4 w-4 mr-2" />
                Creating account...
              </>
            ) : (
              'Create Account'
            )}
          </button>
        </form>

        {/* Switch to Login */}
        {onSwitchToLogin && (
          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Already have an account?{' '}
              <button
                type="button"
                onClick={onSwitchToLogin}
                className="font-medium text-primary-600 hover:text-primary-500"
                disabled={isLoading}
              >
                Sign in
              </button>
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default RegisterForm;
