"""
Test script to check the invite API functionality
"""

import requests
import json

# Test the invite API endpoint
def test_invite_api():
    base_url = "http://localhost:8000"
    
    # You'll need to get a valid token from the browser's localStorage or login
    # For now, let's test without authentication to see the error
    
    group_id = "f52b1265-5beb-46ce-bc7f-d565a2878354"
    email = "<EMAIL>"
    
    url = f"{base_url}/groups/{group_id}/invite-email"
    headers = {
        "Content-Type": "application/json",
        # "Authorization": "Bearer YOUR_TOKEN_HERE"  # Add token when available
    }
    data = {
        "email": email
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 401:
            print("Authentication required - this is expected without a token")
        elif response.status_code == 200:
            print("Invite sent successfully!")
        else:
            print(f"Unexpected response: {response.status_code}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_invite_api()
