import axios from 'axios';
import { ScanReceiptResponse, LoginRequest, RegisterRequest, TokenResponse, User } from '../types';

const API_BASE_URL = 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds timeout
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      const refreshToken = localStorage.getItem('refresh_token');
      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refresh_token: refreshToken
          });

          const { access_token, refresh_token: newRefreshToken } = response.data;
          localStorage.setItem('access_token', access_token);
          localStorage.setItem('refresh_token', newRefreshToken);

          originalRequest.headers.Authorization = `Bearer ${access_token}`;
          return api(originalRequest);
        } catch (refreshError) {
          // Refresh failed, redirect to login
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          window.location.href = '/login';
          return Promise.reject(refreshError);
        }
      }
    }

    return Promise.reject(error);
  }
);

export const receiptApi = {
  /**
   * Upload and scan a receipt image file
   */
  scanReceiptUpload: async (
    file: File, 
    translationLanguage: string = 'English'
  ): Promise<ScanReceiptResponse> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('translation_language', translationLanguage);

    const response = await api.post('/scan-receipt-upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  /**
   * Scan receipt using base64 encoded image
   */
  scanReceiptBase64: async (
    imageBase64: string,
    mimeType: string = 'image/jpeg',
    translationLanguage: string = 'English'
  ): Promise<ScanReceiptResponse> => {
    const response = await api.post('/scan-receipt', {
      image_base64: imageBase64,
      mime_type: mimeType,
      translation_language: translationLanguage,
    });

    return response.data;
  },

  /**
   * Health check
   */
  healthCheck: async () => {
    const response = await api.get('/');
    return response.data;
  },
};

export const authApi = {
  /**
   * Register a new user
   */
  register: async (userData: RegisterRequest): Promise<TokenResponse> => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  /**
   * Login user
   */
  login: async (credentials: LoginRequest): Promise<TokenResponse> => {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },

  /**
   * Logout user
   */
  logout: async (refreshToken: string): Promise<void> => {
    await api.post('/auth/logout', { refresh_token: refreshToken });
  },

  /**
   * Logout from all devices
   */
  logoutAll: async (): Promise<void> => {
    await api.post('/auth/logout-all');
  },

  /**
   * Get current user profile
   */
  getCurrentUser: async (): Promise<User> => {
    const response = await api.get('/auth/me');
    return response.data;
  },

  /**
   * Update user profile
   */
  updateProfile: async (userData: Partial<User>): Promise<User> => {
    const response = await api.put('/auth/me', userData);
    return response.data;
  },

  /**
   * Change password
   */
  changePassword: async (passwordData: {
    current_password: string;
    new_password: string;
    confirm_new_password: string;
  }): Promise<void> => {
    await api.post('/auth/change-password', passwordData);
  },

  /**
   * Validate current token
   */
  validateToken: async (): Promise<{ valid: boolean; user: User }> => {
    const response = await api.get('/auth/validate-token');
    return response.data;
  },
};

export default api;
