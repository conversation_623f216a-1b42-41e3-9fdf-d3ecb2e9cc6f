import React, { useState, useRef } from 'react';
import { Upload, X, FileText, Image, Loader2, AlertCircle } from 'lucide-react';

interface AttachmentInfo {
  filename: string;
  original_name: string;
  size: number;
  content_type: string;
  url: string;
}

interface AttachmentUploaderProps {
  attachments: string[];
  onAttachmentsChange: (attachments: string[]) => void;
  maxFiles?: number;
  className?: string;
}

const AttachmentUploader: React.FC<AttachmentUploaderProps> = ({
  attachments,
  onAttachmentsChange,
  maxFiles = 5,
  className = ''
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [attachmentDetails, setAttachmentDetails] = useState<Record<string, AttachmentInfo>>({});
  const fileInputRef = useRef<HTMLInputElement>(null);

  const ALLOWED_TYPES = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
  const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

  const validateFile = (file: File): string | null => {
    if (!ALLOWED_TYPES.includes(file.type)) {
      return 'Only PDF, JPG, JPEG, and PNG files are allowed.';
    }
    
    if (file.size > MAX_FILE_SIZE) {
      return 'File size must be less than 10MB.';
    }
    
    return null;
  };

  const uploadFile = async (file: File): Promise<AttachmentInfo | null> => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch('http://localhost:8000/upload-attachment', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Upload failed');
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Upload error:', error);
      throw error;
    }
  };

  const handleFileSelect = async (files: FileList) => {
    if (attachments.length + files.length > maxFiles) {
      setUploadError(`Maximum ${maxFiles} files allowed.`);
      return;
    }

    setIsUploading(true);
    setUploadError(null);

    const newAttachments: string[] = [];
    const newDetails: Record<string, AttachmentInfo> = {};

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        // Validate file
        const validationError = validateFile(file);
        if (validationError) {
          setUploadError(validationError);
          continue;
        }

        // Upload file
        const uploadResult = await uploadFile(file);
        if (uploadResult) {
          newAttachments.push(uploadResult.filename);
          newDetails[uploadResult.filename] = uploadResult;
        }
      }

      // Update state
      const updatedAttachments = [...attachments, ...newAttachments];
      onAttachmentsChange(updatedAttachments);
      
      setAttachmentDetails(prev => ({ ...prev, ...newDetails }));
      
    } catch (error: any) {
      setUploadError(error.message || 'Failed to upload files');
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const removeAttachment = (filename: string) => {
    const updatedAttachments = attachments.filter(f => f !== filename);
    onAttachmentsChange(updatedAttachments);
    
    setAttachmentDetails(prev => {
      const updated = { ...prev };
      delete updated[filename];
      return updated;
    });
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (contentType: string) => {
    if (contentType === 'application/pdf') {
      return <FileText className="h-5 w-5 text-red-500" />;
    } else if (contentType.startsWith('image/')) {
      return <Image className="h-5 w-5 text-blue-500" />;
    }
    return <FileText className="h-5 w-5 text-gray-500" />;
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Upload Area */}
      <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 rounded-lg p-4 text-center hover:border-primary-400 dark:hover:border-primary-500 transition-colors">
        {isUploading ? (
          <div className="space-y-2">
            <Loader2 className="h-8 w-8 text-primary-600 dark:text-primary-400 mx-auto animate-spin" />
            <p className="text-sm text-gray-600 dark:text-gray-400">Uploading files...</p>
          </div>
        ) : (
          <div className="space-y-2">
            <Upload className="h-8 w-8 text-gray-400 dark:text-gray-500 mx-auto" />
            <div>
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium"
              >
                Click to upload
              </button>
              <span className="text-gray-500 dark:text-gray-400"> or drag and drop</span>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              PDF, JPG, JPEG, PNG up to 10MB each (max {maxFiles} files)
            </p>
          </div>
        )}
      </div>

      {/* Error Message */}
      {uploadError && (
        <div className="flex items-center space-x-2 text-red-600 dark:text-red-400 text-sm bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 p-2 rounded">
          <AlertCircle className="h-4 w-4" />
          <span>{uploadError}</span>
        </div>
      )}

      {/* Attached Files */}
      {attachments.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Attachments ({attachments.length}/{maxFiles})
          </h4>
          <div className="space-y-2">
            {attachments.map((filename) => {
              const details = attachmentDetails[filename];
              return (
                <div
                  key={filename}
                  className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600"
                >
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    {details && getFileIcon(details.content_type)}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {details?.original_name || filename}
                      </p>
                      {details && (
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {formatFileSize(details.size)}
                        </p>
                      )}
                    </div>
                  </div>

                  <button
                    type="button"
                    onClick={() => removeAttachment(filename)}
                    className="p-1 text-gray-400 dark:text-gray-500 hover:text-red-600 dark:hover:text-red-400 transition-colors"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept=".pdf,.jpg,.jpeg,.png"
        onChange={(e) => {
          if (e.target.files) {
            handleFileSelect(e.target.files);
          }
        }}
        className="hidden"
      />
    </div>
  );
};

export default AttachmentUploader;
