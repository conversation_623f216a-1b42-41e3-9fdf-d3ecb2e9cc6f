"""
Users router for user-related endpoints
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from database.connection import get_db
from database.models import User, GroupMember
from auth.dependencies import get_current_active_user

router = APIRouter(prefix="/users", tags=["users"])

@router.get("/past-group-members", response_model=List[dict])
async def get_past_group_members(
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get users who have been in groups with the current user (friends)
    """
    # Get all users who have been in the same groups as the current user
    result = await db.execute(
        select(User)
        .join(GroupMember, User.id == GroupMember.user_id)
        .where(
            GroupMember.group_id.in_(
                select(GroupMember.group_id)
                .where(GroupMember.user_id == current_user.id)
            )
        )
        .where(User.id != current_user.id)  # Exclude current user
        .distinct()
    )

    friends = []
    for user in result.scalars().all():
        friends.append({
            'id': str(user.id),
            'email': user.email,
            'full_name': user.full_name,
            'username': user.username,
            'avatar_url': user.avatar_url
        })
    
    return friends
