"""
Test script to verify the receipt scanning API is working
"""

import requests
import base64
import os

def test_receipt_api():
    # Test the health check first
    try:
        response = requests.get("http://localhost:8000/")
        print("Health check:", response.status_code)
        if response.status_code == 200:
            print("✅ Backend is running")
        else:
            print("❌ Backend health check failed")
            return
    except Exception as e:
        print(f"❌ Cannot connect to backend: {e}")
        return

    # Check if we have a test image
    test_image = "r1.jpg"
    if not os.path.exists(test_image):
        print(f"❌ Test image {test_image} not found")
        print("Please add a receipt image named 'r1.jpg' to test the API")
        return

    # Test the receipt scanning API
    try:
        with open(test_image, 'rb') as f:
            files = {'file': f}
            data = {'translation_language': 'English'}
            
            print("🔄 Testing receipt scanning API...")
            response = requests.post(
                "http://localhost:8000/scan-receipt-upload",
                files=files,
                data=data,
                timeout=60  # Give it time to process
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Receipt scanning API is working!")
                print(f"Success: {result.get('success')}")
                if result.get('data'):
                    data = result['data']
                    print(f"Merchant: {data.get('merchant_name', 'N/A')}")
                    print(f"Total items: {len(data.get('itemized_list', []))}")
                    print(f"Totals: {len(data.get('totals', []))}")
                else:
                    print("⚠️ No data returned")
            else:
                print(f"❌ API call failed: {response.text}")
                
    except Exception as e:
        print(f"❌ Error testing receipt API: {e}")

if __name__ == "__main__":
    test_receipt_api()
