import React, { useState, useEffect } from 'react';
import { ArrowLeft, Users, Calculator, DollarSign, User, Check } from 'lucide-react';
import { GroupMember, ExpenseItem } from '../types';

interface ExpenseSplitterProps {
  expense: {
    description: string;
    total_amount: number;
    currency: string;
    items: ExpenseItem[];
    payer_id: string;
  };
  members: GroupMember[];
  onBack: () => void;
  onSubmit: (splits: any[]) => void;
  isLoading: boolean;
  error: string | null;
}

type SplitMethod = 'equal' | 'custom' | 'itemized';

interface Split {
  user_id: string;
  amount: number;
  items?: string[]; // Item IDs for itemized splitting
}

const ExpenseSplitter: React.FC<ExpenseSplitterProps> = ({
  expense,
  members,
  onBack,
  onSubmit,
  isLoading,
  error
}) => {
  const [splitMethod, setSplitMethod] = useState<SplitMethod>('equal');
  const [selectedMembers, setSelectedMembers] = useState<string[]>([]);
  const [customSplits, setCustomSplits] = useState<{ [userId: string]: number }>({});
  const [itemAssignments, setItemAssignments] = useState<{ [itemId: string]: string[] }>({});

  useEffect(() => {
    // Initialize with all members selected
    setSelectedMembers(members.map(m => m.user_id));
    
    // Initialize custom splits
    const initialSplits: { [userId: string]: number } = {};
    members.forEach(member => {
      initialSplits[member.user_id] = 0;
    });
    setCustomSplits(initialSplits);

    // Initialize item assignments
    const initialAssignments: { [itemId: string]: string[] } = {};
    expense.items.forEach(item => {
      initialAssignments[item.id] = [];
    });
    setItemAssignments(initialAssignments);
  }, [members, expense.items]);

  const toggleMemberSelection = (userId: string) => {
    setSelectedMembers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const updateCustomSplit = (userId: string, amount: number) => {
    setCustomSplits(prev => ({
      ...prev,
      [userId]: amount
    }));
  };

  const toggleItemAssignment = (itemId: string, userId: string) => {
    setItemAssignments(prev => ({
      ...prev,
      [itemId]: prev[itemId].includes(userId)
        ? prev[itemId].filter(id => id !== userId)
        : [...prev[itemId], userId]
    }));
  };

  const calculateEqualSplits = (): Split[] => {
    const amount = expense.total_amount / selectedMembers.length;
    return selectedMembers.map(userId => ({
      user_id: userId,
      amount: Math.round(amount * 100) / 100
    }));
  };

  const calculateCustomSplits = (): Split[] => {
    return Object.entries(customSplits)
      .filter(([_, amount]) => amount > 0)
      .map(([userId, amount]) => ({
        user_id: userId,
        amount
      }));
  };

  const calculateItemizedSplits = (): Split[] => {
    const userTotals: { [userId: string]: number } = {};
    
    // Calculate total for each user based on assigned items
    expense.items.forEach(item => {
      const assignedUsers = itemAssignments[item.id] || [];
      if (assignedUsers.length > 0) {
        const itemTotal = item.price * item.quantity;
        const perUserAmount = itemTotal / assignedUsers.length;
        
        assignedUsers.forEach(userId => {
          userTotals[userId] = (userTotals[userId] || 0) + perUserAmount;
        });
      }
    });

    return Object.entries(userTotals)
      .filter(([_, amount]) => amount > 0)
      .map(([userId, amount]) => ({
        user_id: userId,
        amount: Math.round(amount * 100) / 100,
        items: expense.items
          .filter(item => itemAssignments[item.id]?.includes(userId))
          .map(item => item.id)
      }));
  };

  const getCurrentSplits = (): Split[] => {
    switch (splitMethod) {
      case 'equal':
        return calculateEqualSplits();
      case 'custom':
        return calculateCustomSplits();
      case 'itemized':
        return calculateItemizedSplits();
      default:
        return [];
    }
  };

  const getTotalSplitAmount = (): number => {
    const splits = getCurrentSplits();
    return splits.reduce((sum, split) => sum + split.amount, 0);
  };

  const isValidSplit = (): boolean => {
    const total = getTotalSplitAmount();
    const difference = Math.abs(total - expense.total_amount);
    return difference < 0.01; // Allow for small rounding differences
  };

  const handleSubmit = () => {
    if (!isValidSplit()) {
      return;
    }
    
    const splits = getCurrentSplits();
    onSubmit(splits);
  };

  const getMemberName = (userId: string): string => {
    const member = members.find(m => m.user_id === userId);
    return member?.user_full_name || member?.user_email || 'Unknown';
  };

  const totalSplitAmount = getTotalSplitAmount();
  const difference = expense.total_amount - totalSplitAmount;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <button
              onClick={onBack}
              className="p-2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors mr-3"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Split Expense</h2>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Expense Summary */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 dark:text-white mb-2">{expense.description}</h3>
            <p className="text-2xl font-bold text-primary-600 dark:text-primary-400">
              {expense.currency} {expense.total_amount.toFixed(2)}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Paid by {getMemberName(expense.payer_id)}
            </p>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
              <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
            </div>
          )}

          {/* Split Method Selection */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-3">How do you want to split this?</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <button
                onClick={() => setSplitMethod('equal')}
                className={`p-4 border-2 rounded-lg text-left transition-colors ${
                  splitMethod === 'equal'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                }`}
              >
                <Users className="h-6 w-6 text-primary-600 dark:text-primary-400 mb-2" />
                <h5 className="font-medium text-gray-900 dark:text-white">Split Equally</h5>
                <p className="text-sm text-gray-500 dark:text-gray-400">Divide evenly among selected members</p>
              </button>

              <button
                onClick={() => setSplitMethod('custom')}
                className={`p-4 border-2 rounded-lg text-left transition-colors ${
                  splitMethod === 'custom'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                }`}
              >
                <Calculator className="h-6 w-6 text-primary-600 dark:text-primary-400 mb-2" />
                <h5 className="font-medium text-gray-900 dark:text-white">Custom Amounts</h5>
                <p className="text-sm text-gray-500 dark:text-gray-400">Enter specific amounts for each person</p>
              </button>

              <button
                onClick={() => setSplitMethod('itemized')}
                className={`p-4 border-2 rounded-lg text-left transition-colors ${
                  splitMethod === 'itemized'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                }`}
                disabled={expense.items.length === 0}
              >
                <DollarSign className="h-6 w-6 text-primary-600 dark:text-primary-400 mb-2" />
                <h5 className="font-medium text-gray-900 dark:text-white">By Items</h5>
                <p className="text-sm text-gray-500 dark:text-gray-400">Assign specific items to people</p>
              </button>
            </div>
          </div>

          {/* Split Configuration */}
          {splitMethod === 'equal' && (
            <div>
              <h5 className="font-medium text-gray-900 dark:text-white mb-3">Select members to include:</h5>
              <div className="space-y-2">
                {members.map(member => (
                  <label key={member.user_id} className="flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={selectedMembers.includes(member.user_id)}
                      onChange={() => toggleMemberSelection(member.user_id)}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded"
                    />
                    <div className="ml-3 flex-1">
                      <p className="font-medium text-gray-900 dark:text-white">
                        {member.user_full_name || member.user_email}
                      </p>
                      {selectedMembers.includes(member.user_id) && (
                        <p className="text-sm text-primary-600 dark:text-primary-400">
                          {expense.currency} {(expense.total_amount / selectedMembers.length).toFixed(2)}
                        </p>
                      )}
                    </div>
                  </label>
                ))}
              </div>
            </div>
          )}

          {splitMethod === 'custom' && (
            <div>
              <h5 className="font-medium text-gray-900 dark:text-white mb-3">Enter custom amounts:</h5>
              <div className="space-y-3">
                {members.map(member => (
                  <div key={member.user_id} className="flex items-center space-x-3">
                    <div className="flex-1">
                      <p className="font-medium text-gray-900 dark:text-white">
                        {member.user_full_name || member.user_email}
                      </p>
                    </div>
                    <div className="w-32">
                      <input
                        type="number"
                        value={customSplits[member.user_id] || ''}
                        onChange={(e) => updateCustomSplit(member.user_id, parseFloat(e.target.value) || 0)}
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {splitMethod === 'itemized' && expense.items.length > 0 && (
            <div>
              <h5 className="font-medium text-gray-900 dark:text-white mb-3">Assign items to members:</h5>
              <div className="space-y-4">
                {expense.items.map(item => (
                  <div key={item.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h6 className="font-medium text-gray-900 dark:text-white">{item.name}</h6>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {expense.currency} {item.price.toFixed(2)} × {item.quantity} = {expense.currency} {(item.price * item.quantity).toFixed(2)}
                        </p>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {members.map(member => (
                        <label key={member.user_id} className="flex items-center p-2 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={itemAssignments[item.id]?.includes(member.user_id) || false}
                            onChange={() => toggleItemAssignment(item.id, member.user_id)}
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded"
                          />
                          <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                            {member.user_full_name || member.user_email}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Split Summary */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h5 className="font-medium text-gray-900 dark:text-white mb-3">Split Summary:</h5>
            <div className="space-y-2">
              {getCurrentSplits().map(split => (
                <div key={split.user_id} className="flex justify-between items-center">
                  <span className="text-gray-700 dark:text-gray-300">{getMemberName(split.user_id)}</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {expense.currency} {split.amount.toFixed(2)}
                  </span>
                </div>
              ))}
            </div>
            <div className="border-t border-gray-200 dark:border-gray-600 pt-2 mt-3">
              <div className="flex justify-between items-center font-medium">
                <span className="text-gray-900 dark:text-white">Total:</span>
                <span className={difference === 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                  {expense.currency} {totalSplitAmount.toFixed(2)}
                </span>
              </div>
              {Math.abs(difference) > 0.01 && (
                <p className="text-sm text-red-600 dark:text-red-400 mt-1">
                  Difference: {expense.currency} {difference.toFixed(2)}
                </p>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={handleSubmit}
              disabled={!isValidSplit() || isLoading}
              className="flex items-center px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Create Expense
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExpenseSplitter;
