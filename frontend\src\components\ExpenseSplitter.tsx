import React, { useState, useEffect } from 'react';
import { ArrowLeft, Users, Calculator, DollarSign, User, Check, Percent } from 'lucide-react';
import { GroupMember, ExpenseItem } from '../types';

interface ExpenseSplitterProps {
  expense: {
    description: string;
    total_amount: number;
    currency: string;
    items: ExpenseItem[];
    payer_id: string;
  };
  members: GroupMember[];
  onBack: () => void;
  onSubmit: (splits: any[]) => void;
  isLoading: boolean;
  error: string | null;
}

type SplitMethod = 'equal' | 'custom' | 'itemized' | 'percentage';

interface Split {
  user_id: string;
  amount: number;
  items?: string[]; // Item IDs for itemized splitting
}

const ExpenseSplitter: React.FC<ExpenseSplitterProps> = ({
  expense,
  members,
  onBack,
  onSubmit,
  isLoading,
  error
}) => {
  const [splitMethod, setSplitMethod] = useState<SplitMethod>('equal');
  const [selectedMembers, setSelectedMembers] = useState<string[]>([]);
  const [customSplits, setCustomSplits] = useState<{ [userId: string]: number }>({});
  const [percentageSplits, setPercentageSplits] = useState<{ [userId: string]: number }>({});
  const [itemAssignments, setItemAssignments] = useState<{ [itemId: string]: string[] }>({});

  useEffect(() => {
    // Initialize with all members selected
    setSelectedMembers(members.map(m => m.user_id));
    
    // Initialize custom splits
    const initialSplits: { [userId: string]: number } = {};
    const initialPercentages: { [userId: string]: number } = {};
    members.forEach(member => {
      initialSplits[member.user_id] = 0;
      initialPercentages[member.user_id] = 0;
    });
    setCustomSplits(initialSplits);
    setPercentageSplits(initialPercentages);

    // Initialize item assignments
    const initialAssignments: { [itemId: string]: string[] } = {};
    expense.items.forEach(item => {
      initialAssignments[item.id] = [];
    });
    setItemAssignments(initialAssignments);
  }, [members, expense.items]);

  const toggleMemberSelection = (userId: string) => {
    setSelectedMembers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const updateCustomSplit = (userId: string, amount: number) => {
    setCustomSplits(prev => ({
      ...prev,
      [userId]: amount
    }));
  };

  const updatePercentageSplit = (userId: string, percentage: number) => {
    setPercentageSplits(prev => ({
      ...prev,
      [userId]: percentage
    }));
  };

  const distributePercentagesEqually = () => {
    const equalPercentage = 100 / members.length;
    const newPercentages: { [userId: string]: number } = {};
    members.forEach(member => {
      newPercentages[member.user_id] = Math.round(equalPercentage * 10) / 10; // Round to 1 decimal
    });
    setPercentageSplits(newPercentages);
  };

  const toggleItemAssignment = (itemId: string, userId: string) => {
    setItemAssignments(prev => ({
      ...prev,
      [itemId]: prev[itemId].includes(userId)
        ? prev[itemId].filter(id => id !== userId)
        : [...prev[itemId], userId]
    }));
  };

  const calculateEqualSplits = (): Split[] => {
    const amount = expense.total_amount / selectedMembers.length;
    return selectedMembers.map(userId => ({
      user_id: userId,
      amount: Math.round(amount * 100) / 100
    }));
  };

  const calculateCustomSplits = (): Split[] => {
    return Object.entries(customSplits)
      .filter(([_, amount]) => amount > 0)
      .map(([userId, amount]) => ({
        user_id: userId,
        amount
      }));
  };

  const calculatePercentageSplits = (): Split[] => {
    return Object.entries(percentageSplits)
      .filter(([_, percentage]) => percentage > 0)
      .map(([userId, percentage]) => ({
        user_id: userId,
        amount: Math.round((expense.total_amount * percentage / 100) * 100) / 100
      }));
  };

  const calculateItemizedSplits = (): Split[] => {
    const userTotals: { [userId: string]: number } = {};
    
    // Calculate total for each user based on assigned items
    expense.items.forEach(item => {
      const assignedUsers = itemAssignments[item.id] || [];
      if (assignedUsers.length > 0) {
        const itemTotal = item.price * item.quantity;
        const perUserAmount = itemTotal / assignedUsers.length;
        
        assignedUsers.forEach(userId => {
          userTotals[userId] = (userTotals[userId] || 0) + perUserAmount;
        });
      }
    });

    return Object.entries(userTotals)
      .filter(([_, amount]) => amount > 0)
      .map(([userId, amount]) => ({
        user_id: userId,
        amount: Math.round(amount * 100) / 100,
        items: expense.items
          .filter(item => itemAssignments[item.id]?.includes(userId))
          .map(item => item.id)
      }));
  };

  const getCurrentSplits = (): Split[] => {
    switch (splitMethod) {
      case 'equal':
        return calculateEqualSplits();
      case 'custom':
        return calculateCustomSplits();
      case 'percentage':
        return calculatePercentageSplits();
      case 'itemized':
        return calculateItemizedSplits();
      default:
        return [];
    }
  };

  const getTotalSplitAmount = (): number => {
    const splits = getCurrentSplits();
    return splits.reduce((sum, split) => sum + split.amount, 0);
  };

  const getTotalPercentage = (): number => {
    return Object.values(percentageSplits).reduce((sum, p) => sum + p, 0);
  };

  const isValidSplit = (): boolean => {
    if (splitMethod === 'percentage') {
      const totalPercentage = Object.values(percentageSplits).reduce((sum, p) => sum + p, 0);
      return Math.abs(totalPercentage - 100) < 0.1; // Allow for small rounding differences
    }

    const total = getTotalSplitAmount();
    const difference = Math.abs(total - expense.total_amount);
    return difference < 0.01; // Allow for small rounding differences
  };

  const handleSubmit = () => {
    if (!isValidSplit()) {
      return;
    }
    
    const splits = getCurrentSplits();
    onSubmit(splits);
  };

  const getMemberName = (userId: string): string => {
    const member = members.find(m => m.user_id === userId);
    return member?.user_full_name || member?.user_email || 'Unknown';
  };

  const totalSplitAmount = getTotalSplitAmount();
  const difference = expense.total_amount - totalSplitAmount;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end sm:items-center justify-center z-50 p-0 sm:p-4">
      <div className="bg-white dark:bg-gray-800 rounded-t-lg sm:rounded-lg shadow-xl w-full sm:max-w-2xl sm:w-full max-h-[95vh] sm:max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <button
              onClick={onBack}
              className="p-2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors mr-3"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Split Expense</h2>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Expense Summary */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 dark:text-white mb-2">{expense.description}</h3>
            <p className="text-2xl font-bold text-primary-600 dark:text-primary-400">
              {expense.currency} {expense.total_amount.toFixed(2)}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Paid by {getMemberName(expense.payer_id)}
            </p>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
              <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
            </div>
          )}

          {/* Split Method Selection */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-3">How do you want to split this?</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <button
                onClick={() => setSplitMethod('equal')}
                className={`p-4 border-2 rounded-lg text-left transition-colors ${
                  splitMethod === 'equal'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                }`}
              >
                <Users className="h-6 w-6 text-primary-600 dark:text-primary-400 mb-2" />
                <h5 className="font-medium text-gray-900 dark:text-white">Split Equally</h5>
                <p className="text-sm text-gray-500 dark:text-gray-400">Divide evenly among selected members</p>
              </button>

              <button
                onClick={() => setSplitMethod('custom')}
                className={`p-4 border-2 rounded-lg text-left transition-colors ${
                  splitMethod === 'custom'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                }`}
              >
                <Calculator className="h-6 w-6 text-primary-600 dark:text-primary-400 mb-2" />
                <h5 className="font-medium text-gray-900 dark:text-white">Custom Amounts</h5>
                <p className="text-sm text-gray-500 dark:text-gray-400">Enter specific amounts for each person</p>
              </button>

              <button
                onClick={() => setSplitMethod('percentage')}
                className={`p-4 border-2 rounded-lg text-left transition-colors ${
                  splitMethod === 'percentage'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                }`}
              >
                <Percent className="h-6 w-6 text-primary-600 dark:text-primary-400 mb-2" />
                <h5 className="font-medium text-gray-900 dark:text-white">By Percentage</h5>
                <p className="text-sm text-gray-500 dark:text-gray-400">Enter percentage for each person</p>
              </button>

              <button
                onClick={() => setSplitMethod('itemized')}
                className={`p-4 border-2 rounded-lg text-left transition-colors ${
                  splitMethod === 'itemized'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                }`}
                disabled={expense.items.length === 0}
              >
                <DollarSign className="h-6 w-6 text-primary-600 dark:text-primary-400 mb-2" />
                <h5 className="font-medium text-gray-900 dark:text-white">By Items</h5>
                <p className="text-sm text-gray-500 dark:text-gray-400">Assign specific items to people</p>
              </button>
            </div>
          </div>

          {/* Split Configuration */}
          {splitMethod === 'equal' && (
            <div>
              <h5 className="font-medium text-gray-900 dark:text-white mb-3">Select members to include:</h5>
              <div className="space-y-2">
                {members.map(member => (
                  <label key={member.user_id} className="flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={selectedMembers.includes(member.user_id)}
                      onChange={() => toggleMemberSelection(member.user_id)}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded"
                    />
                    <div className="ml-3 flex-1">
                      <p className="font-medium text-gray-900 dark:text-white">
                        {member.user_full_name || member.user_email}
                      </p>
                      {selectedMembers.includes(member.user_id) && (
                        <p className="text-sm text-primary-600 dark:text-primary-400">
                          {expense.currency} {(expense.total_amount / selectedMembers.length).toFixed(2)}
                        </p>
                      )}
                    </div>
                  </label>
                ))}
              </div>
            </div>
          )}

          {splitMethod === 'custom' && (
            <div>
              <h5 className="font-medium text-gray-900 dark:text-white mb-3">Enter custom amounts:</h5>
              <div className="space-y-3">
                {members.map(member => (
                  <div key={member.user_id} className="flex items-center space-x-3">
                    <div className="flex-1">
                      <p className="font-medium text-gray-900 dark:text-white">
                        {member.user_full_name || member.user_email}
                      </p>
                    </div>
                    <div className="w-32">
                      <input
                        type="number"
                        value={customSplits[member.user_id] || ''}
                        onChange={(e) => updateCustomSplit(member.user_id, parseFloat(e.target.value) || 0)}
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {splitMethod === 'percentage' && (
            <div>
              <div className="flex items-center justify-between mb-3">
                <h5 className="font-medium text-gray-900 dark:text-white">Enter percentages:</h5>
                <button
                  onClick={distributePercentagesEqually}
                  className="text-sm px-3 py-1 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors"
                >
                  Distribute Equally
                </button>
              </div>
              <div className="space-y-3">
                {members.map(member => (
                  <div key={member.user_id} className="flex items-center space-x-3">
                    <div className="flex-1">
                      <p className="font-medium text-gray-900 dark:text-white">
                        {member.user_full_name || member.user_email}
                      </p>
                    </div>
                    <div className="w-32">
                      <div className="relative">
                        <input
                          type="number"
                          value={percentageSplits[member.user_id] || ''}
                          onChange={(e) => updatePercentageSplit(member.user_id, parseFloat(e.target.value) || 0)}
                          step="0.1"
                          min="0"
                          max="100"
                          placeholder="0.0"
                          className="w-full px-3 py-2 pr-8 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        />
                        <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 text-sm">%</span>
                      </div>
                    </div>
                    <div className="w-24 text-right">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {expense.currency} {((expense.total_amount * (percentageSplits[member.user_id] || 0)) / 100).toFixed(2)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Total percentage:</span>
                  <span className={`font-medium ${
                    Object.values(percentageSplits).reduce((sum, p) => sum + p, 0) === 100
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-red-600 dark:text-red-400'
                  }`}>
                    {Object.values(percentageSplits).reduce((sum, p) => sum + p, 0).toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          )}

          {splitMethod === 'itemized' && expense.items.length > 0 && (
            <div>
              <h5 className="font-medium text-gray-900 dark:text-white mb-3">Assign items to members:</h5>
              <div className="space-y-4">
                {expense.items.map(item => (
                  <div key={item.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h6 className="font-medium text-gray-900 dark:text-white">{item.name}</h6>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {expense.currency} {item.price.toFixed(2)} × {item.quantity} = {expense.currency} {(item.price * item.quantity).toFixed(2)}
                        </p>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {members.map(member => (
                        <label key={member.user_id} className="flex items-center p-2 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={itemAssignments[item.id]?.includes(member.user_id) || false}
                            onChange={() => toggleItemAssignment(item.id, member.user_id)}
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded"
                          />
                          <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                            {member.user_full_name || member.user_email}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Split Summary */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h5 className="font-medium text-gray-900 dark:text-white mb-3">Split Summary:</h5>
            <div className="space-y-2">
              {getCurrentSplits().map(split => (
                <div key={split.user_id} className="flex justify-between items-center">
                  <span className="text-gray-700 dark:text-gray-300">{getMemberName(split.user_id)}</span>
                  <div className="text-right">
                    <span className="font-medium text-gray-900 dark:text-white">
                      {expense.currency} {split.amount.toFixed(2)}
                    </span>
                    {splitMethod === 'percentage' && (
                      <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
                        ({percentageSplits[split.user_id]?.toFixed(1)}%)
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
            <div className="border-t border-gray-200 dark:border-gray-600 pt-2 mt-3">
              <div className="flex justify-between items-center font-medium">
                <span className="text-gray-900 dark:text-white">Total:</span>
                <div className="text-right">
                  <span className={difference === 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                    {expense.currency} {totalSplitAmount.toFixed(2)}
                  </span>
                  {splitMethod === 'percentage' && (
                    <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
                      ({getTotalPercentage().toFixed(1)}%)
                    </span>
                  )}
                </div>
              </div>
              {splitMethod === 'percentage' ? (
                Math.abs(getTotalPercentage() - 100) > 0.1 && (
                  <p className="text-sm text-red-600 dark:text-red-400 mt-1">
                    Percentage must total 100% (currently {getTotalPercentage().toFixed(1)}%)
                  </p>
                )
              ) : (
                Math.abs(difference) > 0.01 && (
                  <p className="text-sm text-red-600 dark:text-red-400 mt-1">
                    Difference: {expense.currency} {difference.toFixed(2)}
                  </p>
                )
              )}
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={handleSubmit}
              disabled={!isValidSplit() || isLoading}
              className="flex items-center px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Create Expense
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExpenseSplitter;
