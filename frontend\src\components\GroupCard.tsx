import React from 'react';
import { Users, Calendar, DollarSign, Crown, Share2 } from 'lucide-react';
import { Group } from '../types';

interface GroupCardProps {
  group: Group;
  onSelect: (group: Group) => void;
  onShare: (group: Group) => void;
  currentUserId?: string;
}

const GroupCard: React.FC<GroupCardProps> = ({
  group,
  onSelect,
  onShare,
  currentUserId,
}) => {
  const isCreator = currentUserId === group.created_by;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200 cursor-pointer"
      onClick={() => onSelect(group)}
    >
      <div className="p-4">
        {/* Mobile Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                {group.name}
              </h3>
              {isCreator && (
                <Crown className="h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" title="You are the creator" />
              )}
            </div>
            {group.description && (
              <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                {group.description}
              </p>
            )}
          </div>

          <button
            onClick={(e) => {
              e.stopPropagation();
              onShare(group);
            }}
            className="p-2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors ml-2"
            title="Share group"
          >
            <Share2 className="h-5 w-5" />
          </button>
        </div>

        {/* Mobile Stats Row */}
        <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-3">
          <div className="flex items-center space-x-1">
            <Users className="h-4 w-4" />
            <span>{group.member_count}</span>
          </div>

          <div className="flex items-center space-x-1">
            <DollarSign className="h-4 w-4" />
            <span>{group.default_currency}</span>
          </div>

          <div className="flex items-center space-x-1">
            <Calendar className="h-4 w-4" />
            <span className="text-xs">{formatDate(group.created_at)}</span>
          </div>
        </div>

        {/* Creator info */}
        <div className="text-xs text-gray-500 dark:text-gray-400 mb-3">
          Created by {group.creator_name || 'Unknown'}
        </div>

        {/* Mobile Action Area */}
        <div className="flex items-center justify-between pt-2 border-t border-gray-100 dark:border-gray-700">
          <span className="text-sm font-medium text-primary-600 dark:text-primary-400">
            Tap to view details
          </span>
          <div className="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
            <span className="text-primary-600 dark:text-primary-400 text-xs">→</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GroupCard;
