import React from 'react';
import { Users, Calendar, DollarSign, Crown, Share2 } from 'lucide-react';
import { Group } from '../types';

interface GroupCardProps {
  group: Group;
  onSelect: (group: Group) => void;
  onShare: (group: Group) => void;
  currentUserId?: string;
}

const GroupCard: React.FC<GroupCardProps> = ({
  group,
  onSelect,
  onShare,
  currentUserId,
}) => {
  const isCreator = currentUserId === group.created_by;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-1">
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                {group.name}
              </h3>
              {isCreator && (
                <Crown className="h-4 w-4 text-yellow-500 flex-shrink-0" title="You are the creator" />
              )}
            </div>
            {group.description && (
              <p className="text-sm text-gray-600 line-clamp-2">
                {group.description}
              </p>
            )}
          </div>
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onShare(group);
            }}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
            title="Share group"
          >
            <Share2 className="h-4 w-4" />
          </button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="flex items-center space-x-2">
            <Users className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600">
              {group.member_count} {group.member_count === 1 ? 'member' : 'members'}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <DollarSign className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600">
              {group.default_currency}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600">
              {formatDate(group.created_at)}
            </span>
          </div>
        </div>

        {/* Creator info */}
        <div className="text-xs text-gray-500 mb-4">
          Created by {group.creator_name || 'Unknown'}
        </div>

        {/* Action button */}
        <button
          onClick={() => onSelect(group)}
          className="w-full px-4 py-2 text-sm font-medium text-primary-700 bg-primary-50 hover:bg-primary-100 rounded-md transition-colors"
        >
          View Group
        </button>
      </div>
    </div>
  );
};

export default GroupCard;
