"""
Pydantic schemas for group management
"""

import uuid
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, validator

class GroupBase(BaseModel):
    """Base group schema"""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    default_currency: str = Field(default="USD", max_length=3)

class GroupCreate(GroupBase):
    """Schema for group creation"""
    pass

class GroupUpdate(BaseModel):
    """Schema for group updates"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    default_currency: Optional[str] = Field(None, max_length=3)

class GroupMemberResponse(BaseModel):
    """Schema for group member response"""
    id: uuid.UUID
    user_id: uuid.UUID
    role: str
    joined_at: datetime
    user_email: str
    user_full_name: Optional[str]
    user_username: Optional[str]
    
    model_config = {"from_attributes": True}

class GroupResponse(GroupBase):
    """Schema for group response"""
    id: uuid.UUID
    invite_code: Optional[str]
    created_by: uuid.UUID
    is_active: bool
    created_at: datetime
    updated_at: datetime
    member_count: int
    creator_name: Optional[str]
    members: Optional[List[GroupMemberResponse]] = None
    
    model_config = {"from_attributes": True}

class GroupListResponse(BaseModel):
    """Schema for group list response"""
    groups: List[GroupResponse]
    total: int

class InviteResponse(BaseModel):
    """Schema for invite response"""
    invite_code: str
    invite_link: str
    qr_code_url: str
    expires_at: Optional[datetime] = None

class JoinGroupRequest(BaseModel):
    """Schema for joining group via invite"""
    invite_code: str = Field(..., min_length=6, max_length=10)

class GroupMemberUpdate(BaseModel):
    """Schema for updating group member"""
    role: str = Field(..., pattern="^(admin|member)$")

class MessageResponse(BaseModel):
    """Schema for simple message responses"""
    message: str
    success: bool = True
