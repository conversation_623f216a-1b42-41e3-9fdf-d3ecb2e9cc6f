"""
Migration script to add payer_id column to expenses table
"""

import sqlite3
import os

def migrate_database():
    db_path = "easysplit.db"
    
    if not os.path.exists(db_path):
        print("Database file not found!")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check if payer_id column already exists
        cursor.execute("PRAGMA table_info(expenses)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'payer_id' not in columns:
            print("Adding payer_id column to expenses table...")
            
            # Add the payer_id column
            cursor.execute("ALTER TABLE expenses ADD COLUMN payer_id TEXT")
            
            # Update existing expenses to set payer_id = created_by
            cursor.execute("UPDATE expenses SET payer_id = created_by WHERE payer_id IS NULL")
            
            conn.commit()
            print("Migration completed successfully!")
        else:
            print("payer_id column already exists!")
            
    except Exception as e:
        print(f"Migration failed: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    migrate_database()
