import React, { useState, useEffect } from 'react';
import { X, UserPlus, Mail, Search, Copy, Share2, QrCode, Check, Users } from 'lucide-react';
import { Group, User } from '../types';
import { groupsApi, usersApi } from '../services/api';

interface InviteMembersProps {
  group: Group;
  isOpen: boolean;
  onClose: () => void;
  onMemberInvited?: () => void;
}

interface Friend {
  id: string;
  email: string;
  full_name?: string;
  username?: string;
  avatar_url?: string;
}

const InviteMembers: React.FC<InviteMembersProps> = ({
  group,
  isOpen,
  onClose,
  onMemberInvited
}) => {
  const [activeTab, setActiveTab] = useState<'friends' | 'email' | 'link'>('friends');
  const [friends, setFriends] = useState<Friend[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [emailInput, setEmailInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [inviteData, setInviteData] = useState<{
    invite_code: string;
    invite_link: string;
    qr_code_url: string;
  } | null>(null);
  const [copiedStates, setCopiedStates] = useState<{[key: string]: boolean}>({});

  useEffect(() => {
    if (isOpen) {
      loadFriends();
      loadInviteData();
    }
  }, [isOpen]);

  const loadFriends = async () => {
    try {
      // Get friends from past groups
      const pastFriends = await usersApi.getPastGroupMembers();
      setFriends(pastFriends);
    } catch (err) {
      console.error('Error loading friends:', err);
      // If API doesn't exist yet, use empty array
      setFriends([]);
    }
  };

  const loadInviteData = async () => {
    try {
      const data = await groupsApi.getGroupInvite(group.id);
      setInviteData(data);
    } catch (err) {
      console.error('Error loading invite data:', err);
    }
  };

  const handleInviteByEmail = async () => {
    if (!emailInput.trim()) return;

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await groupsApi.inviteByEmail(group.id, emailInput.trim());
      setSuccess(`Invitation sent to ${emailInput}`);
      setEmailInput('');
      onMemberInvited?.();
    } catch (err: any) {
      console.error('Error sending invitation:', err);
      setError(err.response?.data?.detail || err.message || 'Failed to send invitation');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInviteFriend = async (friend: Friend) => {
    console.log('handleInviteFriend called with:', friend);
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('Sending invite to:', friend.email, 'for group:', group.id);
      const result = await groupsApi.inviteByEmail(group.id, friend.email);
      console.log('Invite result:', result);
      setSuccess(`Invitation sent to ${friend.full_name || friend.email}`);
      onMemberInvited?.();
    } catch (err: any) {
      console.error('Error sending invitation:', err);
      setError(err.response?.data?.detail || err.message || 'Failed to send invitation');
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = async (text: string, key: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedStates(prev => ({ ...prev, [key]: true }));
      setTimeout(() => {
        setCopiedStates(prev => ({ ...prev, [key]: false }));
      }, 2000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const shareInviteLink = async () => {
    if (!inviteData) return;

    if (navigator.share) {
      try {
        await navigator.share({
          title: `Join ${group.name} on EasySplit`,
          text: `You've been invited to join "${group.name}" for expense sharing!`,
          url: inviteData.invite_link,
        });
      } catch (err) {
        console.error('Error sharing:', err);
      }
    } else {
      // Fallback to copying
      copyToClipboard(inviteData.invite_link, 'share');
    }
  };

  const filteredFriends = friends.filter(friend =>
    friend.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    friend.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    friend.username?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-lg w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Invite Members</h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('friends')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'friends'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Users className="h-4 w-4 inline mr-2" />
              Friends
            </button>
            <button
              onClick={() => setActiveTab('email')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'email'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Mail className="h-4 w-4 inline mr-2" />
              Email
            </button>
            <button
              onClick={() => setActiveTab('link')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'link'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Share2 className="h-4 w-4 inline mr-2" />
              Share Link
            </button>
          </nav>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Error/Success Messages */}
          {error && (
            <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}
          {success && (
            <div className="mb-4 bg-green-50 border border-green-200 rounded-lg p-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          )}

          {/* Friends Tab */}
          {activeTab === 'friends' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Search Friends
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search by name, email, or username..."
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="space-y-2 max-h-60 overflow-y-auto">
                {filteredFriends.length > 0 ? (
                  filteredFriends.map((friend) => (
                    <div key={friend.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-primary-700">
                            {(friend.full_name || friend.email).charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {friend.full_name || friend.username || 'Unknown'}
                          </p>
                          <p className="text-xs text-gray-500">{friend.email}</p>
                        </div>
                      </div>
                      <button
                        onClick={() => handleInviteFriend(friend)}
                        disabled={isLoading}
                        className="px-3 py-1 text-sm bg-primary-600 text-white rounded hover:bg-primary-700 disabled:opacity-50 transition-colors"
                      >
                        Invite
                      </button>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500">No friends found</p>
                    <p className="text-sm text-gray-400 mt-1">
                      Friends are people you've shared groups with before
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Email Tab */}
          {activeTab === 'email' && (
            <div className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address or Username
                </label>
                <input
                  type="text"
                  id="email"
                  value={emailInput}
                  onChange={(e) => setEmailInput(e.target.value)}
                  placeholder="Enter email address or username"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  onKeyPress={(e) => e.key === 'Enter' && handleInviteByEmail()}
                />
              </div>
              <button
                onClick={handleInviteByEmail}
                disabled={!emailInput.trim() || isLoading}
                className="w-full flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <UserPlus className="h-4 w-4 mr-2" />
                {isLoading ? 'Sending...' : 'Send Invitation'}
              </button>
            </div>
          )}

          {/* Share Link Tab */}
          {activeTab === 'link' && inviteData && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Invite Code
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={inviteData.invite_code}
                    readOnly
                    className="flex-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm font-mono"
                  />
                  <button
                    onClick={() => copyToClipboard(inviteData.invite_code, 'code')}
                    className="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                  >
                    {copiedStates.code ? <Check className="h-4 w-4 text-green-600" /> : <Copy className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Invite Link
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={inviteData.invite_link}
                    readOnly
                    className="flex-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm"
                  />
                  <button
                    onClick={() => copyToClipboard(inviteData.invite_link, 'link')}
                    className="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                  >
                    {copiedStates.link ? <Check className="h-4 w-4 text-green-600" /> : <Copy className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={shareInviteLink}
                  className="flex-1 flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
                >
                  <Share2 className="h-4 w-4 mr-2" />
                  Share Link
                </button>
                <button
                  onClick={() => window.open(inviteData.qr_code_url, '_blank')}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  <QrCode className="h-4 w-4" />
                </button>
              </div>

              <div className="text-center">
                <img
                  src={inviteData.qr_code_url}
                  alt="QR Code"
                  className="mx-auto w-32 h-32 border border-gray-200 rounded-lg"
                />
                <p className="text-xs text-gray-500 mt-2">
                  Scan with mobile device to join
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default InviteMembers;
