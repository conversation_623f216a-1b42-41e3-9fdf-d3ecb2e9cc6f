"""
Authentication service with business logic
"""

import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import <PERSON><PERSON>, <PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, or_
from fastapi import HTTPException, status

from database.models import User, RefreshToken
from .schemas import User<PERSON><PERSON>, User<PERSON>ogin, UserUpdate
from .security import (
    get_password_hash, 
    verify_password, 
    create_access_token, 
    create_refresh_token,
    generate_secure_token,
    hash_token,
    validate_password_strength,
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES,
    JWT_REFRESH_TOKEN_EXPIRE_DAYS
)

class AuthService:
    """Authentication service class"""
    
    @staticmethod
    async def register_user(user_data: UserCreate, db: AsyncSession) -> User:
        """
        Register a new user
        
        Args:
            user_data: User registration data
            db: Database session
            
        Returns:
            Created user object
            
        Raises:
            HTTPException: If email/username already exists or validation fails
        """
        # Validate password strength
        password_validation = validate_password_strength(user_data.password)
        if not password_validation["is_valid"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Password does not meet requirements",
                    "errors": password_validation["errors"]
                }
            )
        
        # Check if email already exists
        result = await db.execute(select(User).where(User.email == user_data.email))
        if result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Check if username already exists (if provided)
        if user_data.username:
            result = await db.execute(select(User).where(User.username == user_data.username))
            if result.scalar_one_or_none():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Username already taken"
                )
        
        # Create new user
        hashed_password = get_password_hash(user_data.password)
        
        db_user = User(
            email=user_data.email,
            username=user_data.username,
            full_name=user_data.full_name,
            phone=user_data.phone,
            preferred_currency=user_data.preferred_currency,
            timezone=user_data.timezone,
            password_hash=hashed_password,
            email_verification_token=generate_secure_token(),
        )
        
        db.add(db_user)
        await db.commit()
        await db.refresh(db_user)
        
        return db_user
    
    @staticmethod
    async def authenticate_user(login_data: UserLogin, db: AsyncSession) -> Optional[User]:
        """
        Authenticate user with email/username and password
        
        Args:
            login_data: Login credentials
            db: Database session
            
        Returns:
            User object if authentication successful, None otherwise
        """
        # Find user by email or username
        result = await db.execute(
            select(User).where(
                or_(
                    User.email == login_data.email,
                    User.username == login_data.email  # Allow login with username in email field
                )
            )
        )
        user = result.scalar_one_or_none()
        
        if not user:
            return None
        
        if not verify_password(login_data.password, user.password_hash):
            return None
        
        if not user.is_active:
            return None
        
        # Update last login
        user.last_login = datetime.utcnow()
        await db.commit()
        
        return user
    
    @staticmethod
    async def create_user_tokens(user: User, db: AsyncSession, remember_me: bool = False) -> Tuple[str, str]:
        """
        Create access and refresh tokens for user
        
        Args:
            user: User object
            db: Database session
            remember_me: Whether to extend refresh token lifetime
            
        Returns:
            Tuple of (access_token, refresh_token)
        """
        # Create access token
        access_token_data = {
            "sub": str(user.id),
            "email": user.email,
            "username": user.username,
        }
        access_token = create_access_token(access_token_data)
        
        # Create refresh token
        refresh_token_expires = timedelta(
            days=JWT_REFRESH_TOKEN_EXPIRE_DAYS * (2 if remember_me else 1)
        )
        refresh_token_data = {
            "sub": str(user.id),
        }
        refresh_token = create_refresh_token(refresh_token_data, refresh_token_expires)
        
        # Store refresh token in database
        refresh_token_hash = hash_token(refresh_token)
        db_refresh_token = RefreshToken(
            user_id=user.id,
            token_hash=refresh_token_hash,
            expires_at=datetime.utcnow() + refresh_token_expires,
        )
        
        db.add(db_refresh_token)
        await db.commit()
        
        return access_token, refresh_token
    
    @staticmethod
    async def refresh_access_token(refresh_token: str, db: AsyncSession) -> Optional[Tuple[str, str]]:
        """
        Refresh access token using refresh token
        
        Args:
            refresh_token: Refresh token string
            db: Database session
            
        Returns:
            Tuple of (new_access_token, new_refresh_token) or None if invalid
        """
        from .dependencies import verify_refresh_token
        
        user = await verify_refresh_token(refresh_token, db)
        if not user:
            return None
        
        # Revoke old refresh token
        refresh_token_hash = hash_token(refresh_token)
        result = await db.execute(
            select(RefreshToken).where(RefreshToken.token_hash == refresh_token_hash)
        )
        old_token = result.scalar_one_or_none()
        if old_token:
            old_token.is_revoked = True
        
        # Create new tokens
        new_access_token, new_refresh_token = await AuthService.create_user_tokens(user, db)
        
        return new_access_token, new_refresh_token
    
    @staticmethod
    async def revoke_refresh_token(refresh_token: str, db: AsyncSession) -> bool:
        """
        Revoke a refresh token (logout)
        
        Args:
            refresh_token: Refresh token to revoke
            db: Database session
            
        Returns:
            True if token was revoked, False if not found
        """
        refresh_token_hash = hash_token(refresh_token)
        result = await db.execute(
            select(RefreshToken).where(RefreshToken.token_hash == refresh_token_hash)
        )
        token = result.scalar_one_or_none()
        
        if token:
            token.is_revoked = True
            await db.commit()
            return True
        
        return False
    
    @staticmethod
    async def revoke_all_user_tokens(user_id: uuid.UUID, db: AsyncSession) -> int:
        """
        Revoke all refresh tokens for a user
        
        Args:
            user_id: User ID
            db: Database session
            
        Returns:
            Number of tokens revoked
        """
        result = await db.execute(
            select(RefreshToken).where(
                RefreshToken.user_id == user_id,
                RefreshToken.is_revoked == False
            )
        )
        tokens = result.scalars().all()
        
        for token in tokens:
            token.is_revoked = True
        
        await db.commit()
        return len(tokens)
    
    @staticmethod
    async def update_user_profile(user: User, update_data: UserUpdate, db: AsyncSession) -> User:
        """
        Update user profile
        
        Args:
            user: Current user object
            update_data: Update data
            db: Database session
            
        Returns:
            Updated user object
            
        Raises:
            HTTPException: If username already taken
        """
        # Check if username is being changed and is available
        if update_data.username and update_data.username != user.username:
            result = await db.execute(
                select(User).where(
                    User.username == update_data.username,
                    User.id != user.id
                )
            )
            if result.scalar_one_or_none():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Username already taken"
                )
        
        # Update user fields
        update_dict = update_data.dict(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(user, field, value)
        
        user.updated_at = datetime.utcnow()
        await db.commit()
        await db.refresh(user)
        
        return user

    @staticmethod
    async def change_password(user: User, current_password: str, new_password: str, db: AsyncSession) -> bool:
        """
        Change user password

        Args:
            user: Current user object
            current_password: Current password
            new_password: New password
            db: Database session

        Returns:
            True if password changed successfully

        Raises:
            HTTPException: If current password is incorrect or new password is invalid
        """
        # Verify current password
        if not verify_password(current_password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password is incorrect"
            )

        # Validate new password strength
        password_validation = validate_password_strength(new_password)
        if not password_validation["is_valid"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "New password does not meet requirements",
                    "errors": password_validation["errors"]
                }
            )

        # Update password
        user.password_hash = get_password_hash(new_password)
        user.updated_at = datetime.utcnow()
        await db.commit()

        # Revoke all existing refresh tokens for security
        await AuthService.revoke_all_user_tokens(user.id, db)

        return True
