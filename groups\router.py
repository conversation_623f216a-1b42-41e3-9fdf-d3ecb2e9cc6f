"""
Group management router
"""

import os
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from database.connection import get_db
from database.models import Group, GroupMember, User, GroupInvitation
from auth.dependencies import get_current_active_user
from .schemas import (
    GroupCreate, GroupUpdate, GroupResponse, GroupListResponse,
    InviteResponse, JoinGroupRequest, GroupMemberResponse,
    MessageResponse
)
from .service import GroupService

router = APIRouter(prefix="/groups", tags=["groups"])

@router.post("/", response_model=GroupResponse, status_code=status.HTTP_201_CREATED)
async def create_group(
    group_data: GroupCreate,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new group
    
    - **name**: Group name (required)
    - **description**: Group description (optional)
    - **default_currency**: Default currency for the group (optional, defaults to USD)
    """
    group = await GroupService.create_group(group_data, current_user.id, db)
    await db.refresh(group)

    # Get member count
    result = await db.execute(
        select(func.count(GroupMember.id)).where(GroupMember.group_id == group.id)
    )
    member_count = result.scalar()

    # Create response manually to avoid relationship issues
    return GroupResponse(
        id=group.id,
        name=group.name,
        description=group.description,
        default_currency=group.default_currency,
        invite_code=group.invite_code,
        created_by=group.created_by,
        is_active=group.is_active,
        created_at=group.created_at,
        updated_at=group.updated_at,
        member_count=member_count,
        creator_name=current_user.full_name or current_user.email,
        members=None
    )

@router.get("/", response_model=GroupListResponse)
async def get_user_groups(
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all groups for the current user
    """
    groups = await GroupService.get_user_groups(current_user.id, db)

    group_responses = []
    for group in groups:
        # Get member count
        result = await db.execute(
            select(func.count(GroupMember.id)).where(GroupMember.group_id == group.id)
        )
        member_count = result.scalar()

        # Get creator name
        result = await db.execute(
            select(User).where(User.id == group.created_by)
        )
        creator = result.scalar_one_or_none()
        creator_name = creator.full_name or creator.email if creator else "Unknown"

        # Create response manually
        group_response = GroupResponse(
            id=group.id,
            name=group.name,
            description=group.description,
            default_currency=group.default_currency,
            invite_code=group.invite_code,
            created_by=group.created_by,
            is_active=group.is_active,
            created_at=group.created_at,
            updated_at=group.updated_at,
            member_count=member_count,
            creator_name=creator_name,
            members=None
        )

        group_responses.append(group_response)
    
    return GroupListResponse(
        groups=group_responses,
        total=len(group_responses)
    )

@router.get("/{group_id}", response_model=GroupResponse)
async def get_group(
    group_id: str,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get group details
    """
    try:
        group_uuid = group_id
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid group ID format"
        )
    
    group = await GroupService.get_group_by_id(group_uuid, current_user.id, db)
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )
    
    # Get member count and details
    result = await db.execute(
        select(func.count(GroupMember.id)).where(GroupMember.group_id == group.id)
    )
    member_count = result.scalar()

    # Get creator name
    result = await db.execute(
        select(User).where(User.id == group.created_by)
    )
    creator = result.scalar_one_or_none()
    creator_name = creator.full_name or creator.email if creator else "Unknown"

    # Get members with user details
    result = await db.execute(
        select(GroupMember, User)
        .join(User)
        .where(GroupMember.group_id == group.id)
        .order_by(GroupMember.joined_at)
    )

    member_responses = []
    for member, user in result.all():
        member_response = GroupMemberResponse(
            id=member.id,
            user_id=member.user_id,
            role=member.role,
            joined_at=member.joined_at,
            user_email=user.email,
            user_full_name=user.full_name,
            user_username=user.username
        )
        member_responses.append(member_response)

    # Create response manually
    return GroupResponse(
        id=group.id,
        name=group.name,
        description=group.description,
        default_currency=group.default_currency,
        invite_code=group.invite_code,
        created_by=group.created_by,
        is_active=group.is_active,
        created_at=group.created_at,
        updated_at=group.updated_at,
        member_count=member_count,
        creator_name=creator_name,
        members=member_responses
    )

@router.put("/{group_id}", response_model=GroupResponse)
async def update_group(
    group_id: str,
    group_data: GroupUpdate,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update group (admin only)
    
    - **name**: New group name (optional)
    - **description**: New group description (optional)
    - **default_currency**: New default currency (optional)
    """
    try:
        group_uuid = group_id
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid group ID format"
        )
    
    group = await GroupService.update_group(group_uuid, group_data, current_user.id, db)

    # Get member count
    result = await db.execute(
        select(func.count(GroupMember.id)).where(GroupMember.group_id == group.id)
    )
    member_count = result.scalar()

    # Create response manually
    return GroupResponse(
        id=group.id,
        name=group.name,
        description=group.description,
        default_currency=group.default_currency,
        invite_code=group.invite_code,
        created_by=group.created_by,
        is_active=group.is_active,
        created_at=group.created_at,
        updated_at=group.updated_at,
        member_count=member_count,
        creator_name=current_user.full_name or current_user.email,
        members=None
    )

@router.get("/{group_id}/invite", response_model=InviteResponse)
async def get_group_invite(
    group_id: str,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get group invite link and QR code (members only)
    """
    try:
        group_uuid = group_id
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid group ID format"
        )
    
    group = await GroupService.get_group_by_id(group_uuid, current_user.id, db)
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )
    
    # Generate invite link
    base_url = os.getenv("FRONTEND_URL", "http://localhost:3000")
    invite_link = f"{base_url}/join/{group.invite_code}"
    
    # Generate QR code
    qr_code_url = await GroupService.generate_qr_code(invite_link)
    
    return InviteResponse(
        invite_code=group.invite_code,
        invite_link=invite_link,
        qr_code_url=qr_code_url
    )

@router.post("/{group_id}/regenerate-invite", response_model=InviteResponse)
async def regenerate_group_invite(
    group_id: str,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Regenerate group invite code (admin only)
    """
    try:
        group_uuid = group_id
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid group ID format"
        )
    
    new_invite_code = await GroupService.regenerate_invite_code(group_uuid, current_user.id, db)
    
    # Generate new invite link and QR code
    base_url = os.getenv("FRONTEND_URL", "http://localhost:3000")
    invite_link = f"{base_url}/join/{new_invite_code}"
    qr_code_url = await GroupService.generate_qr_code(invite_link)
    
    return InviteResponse(
        invite_code=new_invite_code,
        invite_link=invite_link,
        qr_code_url=qr_code_url
    )

@router.post("/join", response_model=GroupResponse)
async def join_group(
    join_data: JoinGroupRequest,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Join group using invite code
    
    - **invite_code**: Group invite code
    """
    group = await GroupService.join_group_by_invite(join_data.invite_code, current_user.id, db)

    # Get member count
    result = await db.execute(
        select(func.count(GroupMember.id)).where(GroupMember.group_id == group.id)
    )
    member_count = result.scalar()

    # Get creator name
    result = await db.execute(
        select(User).where(User.id == group.created_by)
    )
    creator = result.scalar_one_or_none()
    creator_name = creator.full_name or creator.email if creator else "Unknown"

    # Create response manually
    return GroupResponse(
        id=group.id,
        name=group.name,
        description=group.description,
        default_currency=group.default_currency,
        invite_code=group.invite_code,
        created_by=group.created_by,
        is_active=group.is_active,
        created_at=group.created_at,
        updated_at=group.updated_at,
        member_count=member_count,
        creator_name=creator_name,
        members=None
    )

@router.delete("/{group_id}", response_model=MessageResponse)
async def delete_group(
    group_id: str,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete group (admin only)
    """
    try:
        group_uuid = group_id
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid group ID format"
        )

    await GroupService.delete_group(group_uuid, current_user.id, db)

    return MessageResponse(
        message="Group deleted successfully",
        success=True
    )

@router.delete("/{group_id}/leave", response_model=MessageResponse)
async def leave_group(
    group_id: str,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Leave group
    """
    try:
        group_uuid = group_id
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid group ID format"
        )

    await GroupService.leave_group(group_uuid, current_user.id, db)

    return MessageResponse(
        message="Successfully left the group",
        success=True
    )

@router.get("/{group_id}/members", response_model=List[GroupMemberResponse])
async def get_group_members(
    group_id: str,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all members of a group
    """
    try:
        group_uuid = group_id
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid group ID format"
        )
    
    # Get members with user details
    result = await db.execute(
        select(GroupMember, User)
        .join(User)
        .where(GroupMember.group_id == group_uuid)
        .order_by(GroupMember.joined_at)
    )

    member_responses = []
    for member, user in result.all():
        member_response = GroupMemberResponse(
            id=member.id,
            user_id=member.user_id,
            role=member.role,
            joined_at=member.joined_at,
            user_email=user.email,
            user_full_name=user.full_name,
            user_username=user.username
        )
        member_responses.append(member_response)
    
    return member_responses

@router.post("/{group_id}/invite-email", response_model=MessageResponse)
async def invite_by_email(
    group_id: str,
    email_data: dict,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Invite member by email
    """
    try:
        group_uuid = group_id
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid group ID format"
        )

    email = email_data.get('email')
    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email is required"
        )

    # Check if user exists
    result = await db.execute(
        select(User).where(User.email == email)
    )
    user = result.scalar_one_or_none()

    if user:
        # Check if user is already a member
        existing_member = await db.execute(
            select(GroupMember).where(
                GroupMember.group_id == group_uuid,
                GroupMember.user_id == user.id
            )
        )
        if existing_member.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User is already a member of this group"
            )

        # Check if there's already a pending invitation
        existing_invitation = await db.execute(
            select(GroupInvitation).where(
                GroupInvitation.group_id == group_uuid,
                GroupInvitation.invitee_id == user.id,
                GroupInvitation.status == "pending"
            )
        )
        if existing_invitation.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User already has a pending invitation to this group"
            )

        # Create invitation request
        invitation = GroupInvitation(
            group_id=group_uuid,
            inviter_id=current_user.id,
            invitee_id=user.id,
            invitee_email=email,
            status="pending"
        )
        db.add(invitation)
        await db.commit()

        return MessageResponse(
            message=f"Invitation sent to {email}",
            success=True
        )
    else:
        # User doesn't exist - create invitation for when they register
        # Check if there's already a pending invitation for this email
        existing_invitation = await db.execute(
            select(GroupInvitation).where(
                GroupInvitation.group_id == group_uuid,
                GroupInvitation.invitee_email == email,
                GroupInvitation.status == "pending"
            )
        )
        if existing_invitation.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="An invitation has already been sent to this email"
            )

        invitation = GroupInvitation(
            group_id=group_uuid,
            inviter_id=current_user.id,
            invitee_id=None,  # User doesn't exist yet
            invitee_email=email,
            status="pending"
        )
        db.add(invitation)
        await db.commit()

        return MessageResponse(
            message=f"Invitation sent to {email} (user will need to register first)",
            success=True
        )

@router.get("/invitations/received", response_model=List[dict])
async def get_received_invitations(
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get invitations received by the current user
    """
    result = await db.execute(
        select(GroupInvitation, Group, User)
        .join(Group, GroupInvitation.group_id == Group.id)
        .join(User, GroupInvitation.inviter_id == User.id)
        .where(
            GroupInvitation.invitee_id == current_user.id,
            GroupInvitation.status == "pending"
        )
        .order_by(GroupInvitation.created_at.desc())
    )

    invitations = []
    for invitation, group, inviter in result.all():
        invitations.append({
            'id': str(invitation.id),
            'group': {
                'id': str(group.id),
                'name': group.name,
                'description': group.description,
                'avatar_url': group.avatar_url
            },
            'inviter': {
                'id': str(inviter.id),
                'full_name': inviter.full_name,
                'email': inviter.email,
                'avatar_url': inviter.avatar_url
            },
            'message': invitation.message,
            'created_at': invitation.created_at.isoformat()
        })

    return invitations

@router.post("/invitations/{invitation_id}/accept", response_model=MessageResponse)
async def accept_invitation(
    invitation_id: str,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Accept a group invitation
    """
    # Get the invitation
    result = await db.execute(
        select(GroupInvitation).where(
            GroupInvitation.id == invitation_id,
            GroupInvitation.invitee_id == current_user.id,
            GroupInvitation.status == "pending"
        )
    )
    invitation = result.scalar_one_or_none()

    if not invitation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invitation not found or already processed"
        )

    # Check if user is already a member
    existing_member = await db.execute(
        select(GroupMember).where(
            GroupMember.group_id == invitation.group_id,
            GroupMember.user_id == current_user.id
        )
    )
    if existing_member.scalar_one_or_none():
        # Update invitation status and return
        invitation.status = "accepted"
        invitation.responded_at = func.now()
        await db.commit()

        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You are already a member of this group"
        )

    # Add user to group
    new_member = GroupMember(
        group_id=invitation.group_id,
        user_id=current_user.id,
        role="member"
    )
    db.add(new_member)

    # Update invitation status
    invitation.status = "accepted"
    invitation.responded_at = func.now()

    await db.commit()

    return MessageResponse(
        message="Invitation accepted successfully",
        success=True
    )

@router.post("/invitations/{invitation_id}/decline", response_model=MessageResponse)
async def decline_invitation(
    invitation_id: str,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Decline a group invitation
    """
    # Get the invitation
    result = await db.execute(
        select(GroupInvitation).where(
            GroupInvitation.id == invitation_id,
            GroupInvitation.invitee_id == current_user.id,
            GroupInvitation.status == "pending"
        )
    )
    invitation = result.scalar_one_or_none()

    if not invitation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invitation not found or already processed"
        )

    # Update invitation status
    invitation.status = "declined"
    invitation.responded_at = func.now()

    await db.commit()

    return MessageResponse(
        message="Invitation declined",
        success=True
    )
