"""
Group management router
"""

import os
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from database.connection import get_db
from database.models import GroupMember, User
from auth.dependencies import get_current_active_user
from .schemas import (
    GroupCreate, GroupUpdate, GroupResponse, GroupListResponse,
    InviteResponse, JoinGroupRequest, GroupMemberResponse,
    MessageResponse
)
from .service import GroupService

router = APIRouter(prefix="/groups", tags=["groups"])

@router.post("/", response_model=GroupResponse, status_code=status.HTTP_201_CREATED)
async def create_group(
    group_data: GroupCreate,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new group
    
    - **name**: Group name (required)
    - **description**: Group description (optional)
    - **default_currency**: Default currency for the group (optional, defaults to USD)
    """
    group = await GroupService.create_group(group_data, current_user.id, db)
    await db.refresh(group)

    # Get member count
    result = await db.execute(
        select(func.count(GroupMember.id)).where(GroupMember.group_id == group.id)
    )
    member_count = result.scalar()

    # Create response manually to avoid relationship issues
    return GroupResponse(
        id=group.id,
        name=group.name,
        description=group.description,
        default_currency=group.default_currency,
        invite_code=group.invite_code,
        created_by=group.created_by,
        is_active=group.is_active,
        created_at=group.created_at,
        updated_at=group.updated_at,
        member_count=member_count,
        creator_name=current_user.full_name or current_user.email,
        members=None
    )

@router.get("/", response_model=GroupListResponse)
async def get_user_groups(
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all groups for the current user
    """
    groups = await GroupService.get_user_groups(current_user.id, db)

    group_responses = []
    for group in groups:
        # Get member count
        result = await db.execute(
            select(func.count(GroupMember.id)).where(GroupMember.group_id == group.id)
        )
        member_count = result.scalar()

        # Get creator name
        result = await db.execute(
            select(User).where(User.id == group.created_by)
        )
        creator = result.scalar_one_or_none()
        creator_name = creator.full_name or creator.email if creator else "Unknown"

        # Create response manually
        group_response = GroupResponse(
            id=group.id,
            name=group.name,
            description=group.description,
            default_currency=group.default_currency,
            invite_code=group.invite_code,
            created_by=group.created_by,
            is_active=group.is_active,
            created_at=group.created_at,
            updated_at=group.updated_at,
            member_count=member_count,
            creator_name=creator_name,
            members=None
        )

        group_responses.append(group_response)
    
    return GroupListResponse(
        groups=group_responses,
        total=len(group_responses)
    )

@router.get("/{group_id}", response_model=GroupResponse)
async def get_group(
    group_id: str,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get group details
    """
    try:
        group_uuid = group_id
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid group ID format"
        )
    
    group = await GroupService.get_group_by_id(group_uuid, current_user.id, db)
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )
    
    # Get member count and details
    result = await db.execute(
        select(func.count(GroupMember.id)).where(GroupMember.group_id == group.id)
    )
    member_count = result.scalar()

    # Get creator name
    result = await db.execute(
        select(User).where(User.id == group.created_by)
    )
    creator = result.scalar_one_or_none()
    creator_name = creator.full_name or creator.email if creator else "Unknown"

    # Get members
    members = await GroupService.get_group_members(group.id, current_user.id, db)
    member_responses = []
    for member in members:
        member_response = GroupMemberResponse(
            id=member.id,
            user_id=member.user_id,
            role=member.role,
            joined_at=member.joined_at,
            user_email=member.user.email,
            user_full_name=member.user.full_name,
            user_username=member.user.username
        )
        member_responses.append(member_response)

    # Create response manually
    return GroupResponse(
        id=group.id,
        name=group.name,
        description=group.description,
        default_currency=group.default_currency,
        invite_code=group.invite_code,
        created_by=group.created_by,
        is_active=group.is_active,
        created_at=group.created_at,
        updated_at=group.updated_at,
        member_count=member_count,
        creator_name=creator_name,
        members=member_responses
    )

@router.put("/{group_id}", response_model=GroupResponse)
async def update_group(
    group_id: str,
    group_data: GroupUpdate,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update group (admin only)
    
    - **name**: New group name (optional)
    - **description**: New group description (optional)
    - **default_currency**: New default currency (optional)
    """
    try:
        group_uuid = group_id
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid group ID format"
        )
    
    group = await GroupService.update_group(group_uuid, group_data, current_user.id, db)

    # Get member count
    result = await db.execute(
        select(func.count(GroupMember.id)).where(GroupMember.group_id == group.id)
    )
    member_count = result.scalar()

    # Create response manually
    return GroupResponse(
        id=group.id,
        name=group.name,
        description=group.description,
        default_currency=group.default_currency,
        invite_code=group.invite_code,
        created_by=group.created_by,
        is_active=group.is_active,
        created_at=group.created_at,
        updated_at=group.updated_at,
        member_count=member_count,
        creator_name=current_user.full_name or current_user.email,
        members=None
    )

@router.get("/{group_id}/invite", response_model=InviteResponse)
async def get_group_invite(
    group_id: str,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get group invite link and QR code (members only)
    """
    try:
        group_uuid = group_id
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid group ID format"
        )
    
    group = await GroupService.get_group_by_id(group_uuid, current_user.id, db)
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )
    
    # Generate invite link
    base_url = os.getenv("FRONTEND_URL", "http://localhost:3000")
    invite_link = f"{base_url}/join/{group.invite_code}"
    
    # Generate QR code
    qr_code_url = await GroupService.generate_qr_code(invite_link)
    
    return InviteResponse(
        invite_code=group.invite_code,
        invite_link=invite_link,
        qr_code_url=qr_code_url
    )

@router.post("/{group_id}/regenerate-invite", response_model=InviteResponse)
async def regenerate_group_invite(
    group_id: str,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Regenerate group invite code (admin only)
    """
    try:
        group_uuid = group_id
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid group ID format"
        )
    
    new_invite_code = await GroupService.regenerate_invite_code(group_uuid, current_user.id, db)
    
    # Generate new invite link and QR code
    base_url = os.getenv("FRONTEND_URL", "http://localhost:3000")
    invite_link = f"{base_url}/join/{new_invite_code}"
    qr_code_url = await GroupService.generate_qr_code(invite_link)
    
    return InviteResponse(
        invite_code=new_invite_code,
        invite_link=invite_link,
        qr_code_url=qr_code_url
    )

@router.post("/join", response_model=GroupResponse)
async def join_group(
    join_data: JoinGroupRequest,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Join group using invite code
    
    - **invite_code**: Group invite code
    """
    group = await GroupService.join_group_by_invite(join_data.invite_code, current_user.id, db)

    # Get member count
    result = await db.execute(
        select(func.count(GroupMember.id)).where(GroupMember.group_id == group.id)
    )
    member_count = result.scalar()

    # Get creator name
    result = await db.execute(
        select(User).where(User.id == group.created_by)
    )
    creator = result.scalar_one_or_none()
    creator_name = creator.full_name or creator.email if creator else "Unknown"

    # Create response manually
    return GroupResponse(
        id=group.id,
        name=group.name,
        description=group.description,
        default_currency=group.default_currency,
        invite_code=group.invite_code,
        created_by=group.created_by,
        is_active=group.is_active,
        created_at=group.created_at,
        updated_at=group.updated_at,
        member_count=member_count,
        creator_name=creator_name,
        members=None
    )

@router.delete("/{group_id}/leave", response_model=MessageResponse)
async def leave_group(
    group_id: str,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Leave group
    """
    try:
        group_uuid = group_id
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid group ID format"
        )
    
    await GroupService.leave_group(group_uuid, current_user.id, db)
    
    return MessageResponse(
        message="Successfully left the group",
        success=True
    )

@router.get("/{group_id}/members", response_model=List[GroupMemberResponse])
async def get_group_members(
    group_id: str,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all members of a group
    """
    try:
        group_uuid = group_id
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid group ID format"
        )
    
    members = await GroupService.get_group_members(group_uuid, current_user.id, db)
    
    member_responses = []
    for member in members:
        member_response = GroupMemberResponse(
            id=member.id,
            user_id=member.user_id,
            role=member.role,
            joined_at=member.joined_at,
            user_email=member.user.email,
            user_full_name=member.user.full_name,
            user_username=member.user.username
        )
        member_responses.append(member_response)
    
    return member_responses
