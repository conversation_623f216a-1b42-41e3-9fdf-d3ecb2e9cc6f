import React, { useState, useEffect } from 'react';
import { ArrowLeft, Users, DollarSign, Calendar, Crown, UserPlus, Settings, Share2, Plus, Receipt } from 'lucide-react';
import { Group, GroupMember, Expense } from '../types';
import { groupsApi, expensesApi } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import GroupInviteModal from './GroupInviteModal';
import GroupSettings from './GroupSettings';
import InviteMembers from './InviteMembers';
import AddExpense from './AddExpense';
import ExpenseCard from './ExpenseCard';

interface GroupDetailsProps {
  groupId: string;
  onBack: () => void;
}

const GroupDetails: React.FC<GroupDetailsProps> = ({ groupId, onBack }) => {
  const [group, setGroup] = useState<Group | null>(null);
  const [members, setMembers] = useState<GroupMember[]>([]);
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [isInviteMembersModalOpen, setIsInviteMembersModalOpen] = useState(false);
  const [isAddExpenseModalOpen, setIsAddExpenseModalOpen] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    loadGroupDetails();
  }, [groupId]);

  const loadGroupDetails = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [groupData, expensesData] = await Promise.all([
        groupsApi.getGroup(groupId),
        expensesApi.getGroupExpenses(groupId)
      ]);
      setGroup(groupData);
      setMembers(groupData.members || []);
      setExpenses(expensesData);
    } catch (err: any) {
      console.error('Error loading group details:', err);
      setError(err.response?.data?.detail || err.message || 'Failed to load group details');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Calculate financial overview
  const calculateFinancialOverview = () => {
    if (!expenses.length || !members.length) {
      return {
        totalExpenses: 0,
        totalAmount: 0,
        currency: group?.default_currency || 'USD',
        balances: {},
        settlements: []
      };
    }

    // Calculate total amount
    const totalAmount = expenses.reduce((sum, expense) => sum + expense.total_amount, 0);
    const currency = expenses[0]?.currency || group?.default_currency || 'USD';

    // Calculate balances for each member
    const balances: { [userId: string]: { name: string, balance: number, paid: number, share: number } } = {};

    // Initialize balances for all members
    members.forEach(member => {
      balances[member.user_id] = {
        name: member.user_full_name || member.user_email,
        balance: 0,
        paid: 0,
        share: 0
      };
    });

    // Calculate what each person paid and owes
    expenses.forEach(expense => {
      // Add to payer's paid amount
      if (balances[expense.payer_id]) {
        balances[expense.payer_id].paid += expense.total_amount;
        balances[expense.payer_id].balance += expense.total_amount;
      }

      // Subtract what each person's share is
      expense.splits.forEach(split => {
        if (balances[split.user_id]) {
          balances[split.user_id].share += split.amount;
          balances[split.user_id].balance -= split.amount;
        }
      });
    });

    // Calculate settlements (who owes who what)
    const settlements: { from: string, to: string, amount: number, fromName: string, toName: string }[] = [];
    const creditors = Object.entries(balances).filter(([_, data]) => data.balance > 0.01);
    const debtors = Object.entries(balances).filter(([_, data]) => data.balance < -0.01);

    // Simple settlement algorithm
    creditors.forEach(([creditorId, creditorData]) => {
      let remainingCredit = creditorData.balance;

      debtors.forEach(([debtorId, debtorData]) => {
        if (remainingCredit > 0.01 && debtorData.balance < -0.01) {
          const settlementAmount = Math.min(remainingCredit, Math.abs(debtorData.balance));

          if (settlementAmount > 0.01) {
            settlements.push({
              from: debtorId,
              to: creditorId,
              amount: settlementAmount,
              fromName: debtorData.name,
              toName: creditorData.name
            });

            remainingCredit -= settlementAmount;
            debtorData.balance += settlementAmount;
          }
        }
      });
    });

    return {
      totalExpenses: expenses.length,
      totalAmount,
      currency,
      balances,
      settlements
    };
  };

  const financialOverview = calculateFinancialOverview();

  const isUserAdmin = (group: Group) => {
    return user?.id === group.created_by;
  };

  const handleGroupUpdated = (updatedGroup: Group) => {
    setGroup(updatedGroup);
  };

  const handleMemberInvited = () => {
    // Refresh group data to get updated member count
    loadGroupDetails();
  };

  const handleExpenseAdded = () => {
    // Refresh group data to get updated expense count
    loadGroupDetails();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading group details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <h3 className="text-lg font-medium text-red-800 mb-2">Error Loading Group</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <div className="space-x-3">
            <button
              onClick={loadGroupDetails}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
            <button
              onClick={onBack}
              className="px-4 py-2 text-red-600 bg-red-50 hover:bg-red-100 rounded-md transition-colors"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!group) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600 dark:text-gray-400">Group not found</p>
        <button
          onClick={onBack}
          className="mt-4 px-4 py-2 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 hover:bg-primary-100 dark:hover:bg-primary-900/30 rounded-md transition-colors"
        >
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="p-2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{group.name}</h1>
            {group.description && (
              <p className="text-gray-600 dark:text-gray-400 mt-1">{group.description}</p>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsAddExpenseModalOpen(true)}
            className="flex items-center px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md transition-colors"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Expense
          </button>
          <button
            onClick={() => setIsInviteModalOpen(true)}
            className="flex items-center px-3 py-2 text-sm font-medium text-primary-700 dark:text-primary-300 bg-primary-50 dark:bg-primary-900/20 hover:bg-primary-100 dark:hover:bg-primary-900/30 rounded-md transition-colors"
          >
            <Share2 className="h-4 w-4 mr-1" />
            Share
          </button>
          <button
            onClick={() => setIsSettingsModalOpen(true)}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors"
          >
            <Settings className="h-4 w-4 mr-1" />
            Settings
          </button>
        </div>
      </div>

      {/* Financial Overview */}
      {expenses.length > 0 && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Financial Overview</h2>

          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-100 dark:border-blue-800">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                  <Receipt className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{financialOverview.totalExpenses}</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Total Expenses</p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-green-100 dark:border-green-800">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                  <DollarSign className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {financialOverview.currency} {financialOverview.totalAmount.toFixed(2)}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Total Amount</p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-purple-100 dark:border-purple-800">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                  <Users className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {financialOverview.currency} {(financialOverview.totalAmount / members.length).toFixed(2)}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Per Person</p>
                </div>
              </div>
            </div>
          </div>

          {/* Balances and Settlements */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Member Balances */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <h3 className="text-md font-semibold text-gray-900 dark:text-white mb-3">Member Balances</h3>
              <div className="space-y-3">
                {Object.entries(financialOverview.balances).map(([userId, data]) => (
                  <div key={userId} className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                        <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                          {data.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">{data.name}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Paid: {financialOverview.currency} {data.paid.toFixed(2)} •
                          Share: {financialOverview.currency} {data.share.toFixed(2)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`text-sm font-semibold ${
                        data.balance > 0.01 ? 'text-green-600 dark:text-green-400' :
                        data.balance < -0.01 ? 'text-red-600 dark:text-red-400' : 'text-gray-600 dark:text-gray-400'
                      }`}>
                        {data.balance > 0.01 ? '+' : ''}{financialOverview.currency} {data.balance.toFixed(2)}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {data.balance > 0.01 ? 'Gets back' : data.balance < -0.01 ? 'Owes' : 'Settled'}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Settlements */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <h3 className="text-md font-semibold text-gray-900 dark:text-white mb-3">Suggested Settlements</h3>
              {financialOverview.settlements.length > 0 ? (
                <div className="space-y-3">
                  {financialOverview.settlements.map((settlement, index) => (
                    <div key={index} className="flex items-center justify-between py-3 px-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center">
                          <span className="text-xs font-medium text-yellow-700 dark:text-yellow-300">
                            {settlement.fromName.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {settlement.fromName} → {settlement.toName}
                          </p>
                          <p className="text-xs text-gray-600 dark:text-gray-400">Settlement payment</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-bold text-yellow-700 dark:text-yellow-300">
                          {financialOverview.currency} {settlement.amount.toFixed(2)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-2">
                    <span className="text-green-600 dark:text-green-400 text-lg">✓</span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">All settled up!</p>
                  <p className="text-xs text-gray-500 dark:text-gray-500">No outstanding balances</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Group Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-3">
            <Users className="h-8 w-8 text-primary-600 dark:text-primary-400" />
            <div>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{group.member_count}</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Members</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-3">
            <DollarSign className="h-8 w-8 text-green-600 dark:text-green-400" />
            <div>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{group.default_currency}</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Currency</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-3">
            <Calendar className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">Created</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">{formatDate(group.created_at)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Members Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Members</h2>
            <button
              onClick={() => setIsInviteMembersModalOpen(true)}
              className="flex items-center px-3 py-2 text-sm font-medium text-primary-700 dark:text-primary-300 bg-primary-50 dark:bg-primary-900/20 hover:bg-primary-100 dark:hover:bg-primary-900/30 rounded-md transition-colors"
            >
              <UserPlus className="h-4 w-4 mr-1" />
              Invite Members
            </button>
          </div>
        </div>
        
        <div className="p-6">
          {members.length > 0 ? (
            <div className="space-y-4">
              {members.map((member) => (
                <div key={member.id} className="flex items-center justify-between py-3 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-primary-700 dark:text-primary-300">
                        {(member.user_full_name || member.user_email || 'U').charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {member.user_full_name || member.user_email}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Joined {formatDate(member.joined_at)}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {member.role === 'admin' && (
                      <Crown className="h-4 w-4 text-yellow-500 dark:text-yellow-400" title="Admin" />
                    )}
                    <span className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full capitalize">
                      {member.role}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">No members found</p>
            </div>
          )}
        </div>
      </div>

      {/* Expenses Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Recent Expenses</h2>
        </div>
        <div className="p-6">
          {expenses.length > 0 ? (
            <div className="space-y-3">
              {expenses.map((expense) => (
                <ExpenseCard
                  key={expense.id}
                  expense={expense}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <DollarSign className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">No expenses yet</p>
              <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">Start by adding your first expense</p>
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      {group && (
        <>
          <GroupInviteModal
            isOpen={isInviteModalOpen}
            onClose={() => setIsInviteModalOpen(false)}
            group={group}
            isAdmin={isUserAdmin(group)}
          />

          <GroupSettings
            group={group}
            isOpen={isSettingsModalOpen}
            onClose={() => setIsSettingsModalOpen(false)}
            onGroupUpdated={handleGroupUpdated}
            isAdmin={isUserAdmin(group)}
          />

          <InviteMembers
            group={group}
            isOpen={isInviteMembersModalOpen}
            onClose={() => setIsInviteMembersModalOpen(false)}
            onMemberInvited={handleMemberInvited}
          />

          <AddExpense
            group={group}
            members={members}
            isOpen={isAddExpenseModalOpen}
            onClose={() => setIsAddExpenseModalOpen(false)}
            onExpenseAdded={handleExpenseAdded}
          />
        </>
      )}
    </div>
  );
};

export default GroupDetails;
