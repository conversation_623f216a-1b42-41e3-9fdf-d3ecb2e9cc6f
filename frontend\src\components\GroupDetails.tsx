import React, { useState, useEffect } from 'react';
import { ArrowLeft, Users, DollarSign, Calendar, Crown, UserPlus, Settings, Share2 } from 'lucide-react';
import { Group, GroupMember } from '../types';
import { groupsApi } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import GroupInviteModal from './GroupInviteModal';
import GroupSettings from './GroupSettings';
import InviteMembers from './InviteMembers';

interface GroupDetailsProps {
  groupId: string;
  onBack: () => void;
}

const GroupDetails: React.FC<GroupDetailsProps> = ({ groupId, onBack }) => {
  const [group, setGroup] = useState<Group | null>(null);
  const [members, setMembers] = useState<GroupMember[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [isInviteMembersModalOpen, setIsInviteMembersModalOpen] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    loadGroupDetails();
  }, [groupId]);

  const loadGroupDetails = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const groupData = await groupsApi.getGroup(groupId);
      setGroup(groupData);
      setMembers(groupData.members || []);
    } catch (err: any) {
      console.error('Error loading group details:', err);
      setError(err.response?.data?.detail || err.message || 'Failed to load group details');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const isUserAdmin = (group: Group) => {
    return user?.id === group.created_by;
  };

  const handleGroupUpdated = (updatedGroup: Group) => {
    setGroup(updatedGroup);
  };

  const handleMemberInvited = () => {
    // Refresh group data to get updated member count
    loadGroupDetails();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading group details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <h3 className="text-lg font-medium text-red-800 mb-2">Error Loading Group</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <div className="space-x-3">
            <button
              onClick={loadGroupDetails}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
            <button
              onClick={onBack}
              className="px-4 py-2 text-red-600 bg-red-50 hover:bg-red-100 rounded-md transition-colors"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!group) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">Group not found</p>
        <button
          onClick={onBack}
          className="mt-4 px-4 py-2 text-primary-600 bg-primary-50 hover:bg-primary-100 rounded-md transition-colors"
        >
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{group.name}</h1>
            {group.description && (
              <p className="text-gray-600 mt-1">{group.description}</p>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsInviteModalOpen(true)}
            className="flex items-center px-3 py-2 text-sm font-medium text-primary-700 bg-primary-50 hover:bg-primary-100 rounded-md transition-colors"
          >
            <Share2 className="h-4 w-4 mr-1" />
            Share
          </button>
          <button
            onClick={() => setIsSettingsModalOpen(true)}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            <Settings className="h-4 w-4 mr-1" />
            Settings
          </button>
        </div>
      </div>

      {/* Group Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center space-x-3">
            <Users className="h-8 w-8 text-primary-600" />
            <div>
              <p className="text-2xl font-bold text-gray-900">{group.member_count}</p>
              <p className="text-sm text-gray-600">Members</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center space-x-3">
            <DollarSign className="h-8 w-8 text-green-600" />
            <div>
              <p className="text-2xl font-bold text-gray-900">{group.default_currency}</p>
              <p className="text-sm text-gray-600">Currency</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center space-x-3">
            <Calendar className="h-8 w-8 text-blue-600" />
            <div>
              <p className="text-sm font-medium text-gray-900">Created</p>
              <p className="text-sm text-gray-600">{formatDate(group.created_at)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Members Section */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Members</h2>
            <button
              onClick={() => setIsInviteMembersModalOpen(true)}
              className="flex items-center px-3 py-2 text-sm font-medium text-primary-700 bg-primary-50 hover:bg-primary-100 rounded-md transition-colors"
            >
              <UserPlus className="h-4 w-4 mr-1" />
              Invite Members
            </button>
          </div>
        </div>
        
        <div className="p-6">
          {members.length > 0 ? (
            <div className="space-y-4">
              {members.map((member) => (
                <div key={member.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-primary-700">
                        {(member.user_full_name || member.user_email || 'U').charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {member.user_full_name || member.user_email}
                      </p>
                      <p className="text-xs text-gray-500">
                        Joined {formatDate(member.joined_at)}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {member.role === 'admin' && (
                      <Crown className="h-4 w-4 text-yellow-500" title="Admin" />
                    )}
                    <span className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded-full capitalize">
                      {member.role}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">No members found</p>
            </div>
          )}
        </div>
      </div>

      {/* Expenses Section - Placeholder */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Recent Expenses</h2>
        </div>
        <div className="p-6">
          <div className="text-center py-8">
            <DollarSign className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">No expenses yet</p>
            <p className="text-sm text-gray-400 mt-1">Start by adding your first expense</p>
          </div>
        </div>
      </div>

      {/* Modals */}
      {group && (
        <>
          <GroupInviteModal
            isOpen={isInviteModalOpen}
            onClose={() => setIsInviteModalOpen(false)}
            group={group}
            isAdmin={isUserAdmin(group)}
          />

          <GroupSettings
            group={group}
            isOpen={isSettingsModalOpen}
            onClose={() => setIsSettingsModalOpen(false)}
            onGroupUpdated={handleGroupUpdated}
            isAdmin={isUserAdmin(group)}
          />

          <InviteMembers
            group={group}
            isOpen={isInviteMembersModalOpen}
            onClose={() => setIsInviteMembersModalOpen(false)}
            onMemberInvited={handleMemberInvited}
          />
        </>
      )}
    </div>
  );
};

export default GroupDetails;
