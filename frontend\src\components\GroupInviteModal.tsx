import React, { useState, useEffect } from 'react';
import { X, Co<PERSON>, RefreshCw, QrCode, Link, Check, Loader2 } from 'lucide-react';
import { Group, InviteResponse } from '../types';
import { groupsApi } from '../services/api';

interface GroupInviteModalProps {
  isOpen: boolean;
  onClose: () => void;
  group: Group;
  isAdmin?: boolean;
}

const GroupInviteModal: React.FC<GroupInviteModalProps> = ({
  isOpen,
  onClose,
  group,
  isAdmin = false,
}) => {
  const [inviteData, setInviteData] = useState<InviteResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [copiedField, setCopiedField] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      loadInviteData();
    }
  }, [isOpen, group.id]);

  const loadInviteData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await groupsApi.getGroupInvite(group.id);
      setInviteData(data);
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Failed to load invite data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegenerateInvite = async () => {
    setIsRegenerating(true);
    setError(null);
    try {
      const data = await groupsApi.regenerateInvite(group.id);
      setInviteData(data);
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Failed to regenerate invite');
    } finally {
      setIsRegenerating(false);
    }
  };

  const copyToClipboard = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(field);
      setTimeout(() => setCopiedField(null), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Invite to {group.name}</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
          >
            <X className="h-5 w-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="animate-spin h-8 w-8 text-primary-600" />
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-red-600 mb-4">{error}</p>
              <button
                onClick={loadInviteData}
                className="px-4 py-2 text-sm font-medium text-primary-700 bg-primary-50 hover:bg-primary-100 rounded-md transition-colors"
              >
                Try Again
              </button>
            </div>
          ) : inviteData ? (
            <>
              {/* Invite Code */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Invite Code
                </label>
                <div className="flex items-center space-x-2">
                  <div className="flex-1 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white rounded-md font-mono text-lg text-center">
                    {inviteData.invite_code}
                  </div>
                  <button
                    onClick={() => copyToClipboard(inviteData.invite_code, 'code')}
                    className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
                    title="Copy invite code"
                  >
                    {copiedField === 'code' ? (
                      <Check className="h-5 w-5 text-green-500" />
                    ) : (
                      <Copy className="h-5 w-5" />
                    )}
                  </button>
                </div>
              </div>

              {/* Invite Link */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Invite Link
                </label>
                <div className="flex items-center space-x-2">
                  <div className="flex-1 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white rounded-md text-sm truncate">
                    {inviteData.invite_link}
                  </div>
                  <button
                    onClick={() => copyToClipboard(inviteData.invite_link, 'link')}
                    className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
                    title="Copy invite link"
                  >
                    {copiedField === 'link' ? (
                      <Check className="h-5 w-5 text-green-500" />
                    ) : (
                      <Link className="h-5 w-5" />
                    )}
                  </button>
                </div>
              </div>

              {/* QR Code */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  QR Code
                </label>
                <div className="flex justify-center p-4 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
                  <img
                    src={inviteData.qr_code_url}
                    alt="QR Code for group invite"
                    className="w-48 h-48"
                  />
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-2">
                  Scan this QR code to join the group
                </p>
              </div>

              {/* Admin Actions */}
              {isAdmin && (
                <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                  <button
                    onClick={handleRegenerateInvite}
                    disabled={isRegenerating}
                    className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-yellow-700 dark:text-yellow-300 bg-yellow-50 dark:bg-yellow-900/20 hover:bg-yellow-100 dark:hover:bg-yellow-900/30 rounded-md transition-colors disabled:opacity-50"
                  >
                    {isRegenerating ? (
                      <>
                        <Loader2 className="animate-spin h-4 w-4 mr-2" />
                        Regenerating...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Regenerate Invite Code
                      </>
                    )}
                  </button>
                  <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-2">
                    This will invalidate the current invite code
                  </p>
                </div>
              )}

              {/* Instructions */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-4">
                <h4 className="text-sm font-medium text-blue-900 dark:text-blue-300 mb-2">How to invite people:</h4>
                <ul className="text-sm text-blue-800 dark:text-blue-400 space-y-1">
                  <li>• Share the invite code or link</li>
                  <li>• Let them scan the QR code</li>
                  <li>• They can join using the EasySplit app</li>
                </ul>
              </div>
            </>
          ) : null}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <button
            onClick={onClose}
            className="w-full px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-md transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default GroupInviteModal;
